batch_size=1000
val_batch_size=1000
num_epochs=600
mse_mult=10000
rec_fmri_mult=2.0
rec_eeg_mult=2.0
missing_prob=0.75

model_name="MIDAS"
exp_name=$model_name'_fmri17_7wm.75_eeg78_half'
use_modalities=("fmri" "eeg" "image" "text")
dataset_name="nsd"
gpu_id=1


CUDA_VISIBLE_DEVICES=$gpu_id python train.py \
--exp_name $exp_name --model_name $model_name --dataset_name $dataset_name --fmri_subj_list 1 7 --eeg_subj_list 7 8 \
--num_epochs $num_epochs --batch_size $batch_size --val_batch_size $val_batch_size \
--h_size 2048 --n_blocks 8 --pool_type max --pool_num 8192 --eeg_size 1900 --missing_prob $missing_prob \
--mse_mult $mse_mult --rec_fmri_mult $rec_fmri_mult --rec_eeg_mult $rec_eeg_mult \
--eval_interval 10 --ckpt_interval 50 \
--max_lr 1.5e-4 --num_workers 4 --use_modalities "${use_modalities[@]}"