subj_test="eeg_7"
model_name="MoPoE"
exp_name=$model_name'_fmri_eeg'
ckpt_from="/opt/data/private/lq/code/multivae/train_logs_fmri_eeg/$exp_name/last.pth"
use_modalities=("fmri" "image" "text" "eeg")
dataset_name="nsd"
text_image_ratio=0.5
guidance=5
gpu_id=0


CUDA_VISIBLE_DEVICES=$gpu_id python -W ignore \
recon.py \
--model_name $model_name --ckpt_from $ckpt_from \
--fmri_trainer_list 1 5 --eeg_trainer_list 7 8 \
--h_size 2048 --n_blocks 4 --pool_type max --pool_num 8192 --eeg_size 1900 \
--subj_test $subj_test \
--text_image_ratio $text_image_ratio --guidance $guidance \
--recons_per_sample 2 --exp_name $exp_name  --dataset_name $dataset_name  --use_modalities "${use_modalities[@]}"