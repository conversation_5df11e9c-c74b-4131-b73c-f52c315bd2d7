<?xml version="1.0" encoding="UTF-8"?>
<svg width="1600" height="1200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .input-box { fill: #E3F2FD; stroke: #1976D2; stroke-width: 2; }
      .encoder-box { fill: #F3E5F5; stroke: #7B1FA2; stroke-width: 2; }
      .latent-box { fill: #FFF3E0; stroke: #F57C00; stroke-width: 2; }
      .decoder-box { fill: #E8F5E8; stroke: #388E3C; stroke-width: 2; }
      .loss-box { fill: #FFEBEE; stroke: #D32F2F; stroke-width: 2; }
      .output-box { fill: #F1F8E9; stroke: #689F38; stroke-width: 2; }
      .subject-box { fill: #FCE4EC; stroke: #C2185B; stroke-width: 2; }
      .section-bg { fill: #F8F9FA; stroke: #DEE2E6; stroke-width: 1; stroke-dasharray: 3,3; opacity: 0.7; }
      .text { font-family: Arial, sans-serif; font-size: 12px; text-anchor: middle; }
      .small-text { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
      .title { font-family: Arial, sans-serif; font-size: 14px; font-weight: bold; text-anchor: middle; }
      .section-title { font-family: Arial, sans-serif; font-size: 16px; font-weight: bold; text-anchor: middle; }
      .main-title { font-family: Arial, sans-serif; font-size: 18px; font-weight: bold; text-anchor: middle; }
      .main-arrow { stroke: #2E7D32; stroke-width: 3; fill: none; marker-end: url(#main-arrowhead); }
      .data-arrow { stroke: #1976D2; stroke-width: 2; fill: none; marker-end: url(#data-arrowhead); }
      .control-arrow { stroke: #F57C00; stroke-width: 2; fill: none; stroke-dasharray: 5,5; marker-end: url(#control-arrowhead); }
      .loss-arrow { stroke: #D32F2F; stroke-width: 2; fill: none; marker-end: url(#loss-arrowhead); }
    </style>
    <marker id="main-arrowhead" markerWidth="12" markerHeight="9" refX="11" refY="4.5" orient="auto">
      <polygon points="0 0, 12 4.5, 0 9" fill="#2E7D32" />
    </marker>
    <marker id="data-arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#1976D2" />
    </marker>
    <marker id="control-arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#F57C00" />
    </marker>
    <marker id="loss-arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#D32F2F" />
    </marker>
  </defs>

  <!-- Main Title -->
  <text x="800" y="30" class="main-title">MMVAEPlus_DIS Architecture - "dreg_looser" Objective</text>

  <!-- Section 1: Input Layer -->
  <rect x="50" y="60" width="700" height="80" class="section-bg" />
  <text x="400" y="80" class="section-title">1. Input Modalities</text>

  <rect x="80" y="100" width="80" height="30" class="input-box" />
  <text x="120" y="120" class="text">fMRI</text>

  <rect x="180" y="100" width="80" height="30" class="input-box" />
  <text x="220" y="120" class="text">EEG</text>

  <rect x="280" y="100" width="80" height="30" class="input-box" />
  <text x="320" y="120" class="text">Image</text>

  <rect x="380" y="100" width="80" height="30" class="input-box" />
  <text x="420" y="120" class="text">Text</text>

  <rect x="500" y="100" width="100" height="30" class="subject-box" />
  <text x="550" y="120" class="text">subj_index</text>

  <!-- Section 2: Encoding Stage -->
  <rect x="50" y="160" width="700" height="120" class="section-bg" />
  <text x="400" y="180" class="section-title">2. Encoding Stage</text>

  <!-- Modality-Specific Encoders -->
  <rect x="80" y="200" width="80" height="30" class="encoder-box" />
  <text x="120" y="220" class="text">Enc_fMRI</text>

  <rect x="180" y="200" width="80" height="30" class="encoder-box" />
  <text x="220" y="220" class="text">Enc_EEG</text>

  <rect x="280" y="200" width="80" height="30" class="encoder-box" />
  <text x="320" y="220" class="text">Enc_Image</text>

  <rect x="380" y="200" width="80" height="30" class="encoder-box" />
  <text x="420" y="220" class="text">Enc_Text</text>

  <!-- Subject Encoder -->
  <rect x="500" y="200" width="100" height="30" class="subject-box" />
  <text x="550" y="220" class="text">Enc_Subject</text>

  <!-- Union Encoder -->
  <rect x="250" y="250" width="150" height="30" class="encoder-box" />
  <text x="325" y="270" class="text">Union Encoder</text>

  <!-- Section 3: Latent Space Processing -->
  <rect x="50" y="300" width="700" height="180" class="section-bg" />
  <text x="400" y="320" class="section-title">3. Latent Space Processing</text>

  <!-- Embedding Splitting -->
  <text x="200" y="345" class="title">Embedding Splitting</text>
  <rect x="80" y="355" width="100" height="35" class="latent-box" />
  <text x="130" y="375" class="text">Shared (μ_u, σ_u)</text>

  <rect x="200" y="355" width="100" height="35" class="latent-box" />
  <text x="250" y="375" class="text">Private (μ_w, σ_w)</text>

  <rect x="320" y="355" width="100" height="35" class="latent-box" />
  <text x="370" y="375" class="text">Subject (μ_s, σ_s)</text>

  <!-- Subject Processing -->
  <rect x="450" y="340" width="120" height="50" class="subject-box" />
  <text x="510" y="360" class="text">Subject Processing</text>
  <text x="510" y="375" class="small-text">brain_b = (brain_b_ + sub_b)/2</text>

  <!-- Posterior Distributions with DReG -->
  <text x="200" y="415" class="title">Posterior Distributions (DReG detached)</text>
  <rect x="80" y="425" width="100" height="35" class="latent-box" />
  <text x="130" y="445" class="text">q_u(z_u|x)</text>

  <rect x="200" y="425" width="100" height="35" class="latent-box" />
  <text x="250" y="445" class="text">q_w(z_w|x)</text>

  <!-- K-Sampling -->
  <text x="450" y="415" class="title">K-Sampling</text>
  <rect x="380" y="425" width="120" height="35" class="latent-box" />
  <text x="440" y="445" class="text">u_x, w_x ~ [K samples]</text>

  <!-- Section 4: Reconstruction &amp; Decoding -->
  <rect x="50" y="500" width="700" height="140" class="section-bg" />
  <text x="400" y="520" class="section-title">4. Reconstruction &amp; Decoding</text>

  <!-- Reconstruction Paths -->
  <rect x="80" y="540" width="140" height="40" class="decoder-box" />
  <text x="150" y="555" class="text">Self-Reconstruction</text>
  <text x="150" y="570" class="small-text">z = [u_x, w_x, brain_target]</text>

  <rect x="240" y="540" width="140" height="40" class="decoder-box" />
  <text x="310" y="555" class="text">Cross-Modal Recon</text>
  <text x="310" y="570" class="small-text">z = [u_x, w_prior, brain_target]</text>

  <!-- Union Decoder -->
  <rect x="400" y="540" width="120" height="40" class="decoder-box" />
  <text x="460" y="565" class="text">Union Decoder</text>

  <!-- Modality-Specific Decoders -->
  <rect x="80" y="590" width="80" height="30" class="decoder-box" />
  <text x="120" y="610" class="text">Dec_fMRI</text>

  <rect x="180" y="590" width="80" height="30" class="decoder-box" />
  <text x="220" y="610" class="text">Dec_EEG</text>

  <rect x="280" y="590" width="80" height="30" class="decoder-box" />
  <text x="320" y="610" class="text">Dec_Image</text>

  <rect x="380" y="590" width="80" height="30" class="decoder-box" />
  <text x="420" y="610" class="text">Dec_Text</text>

  <!-- Section 5: DReG Loss Computation -->
  <rect x="50" y="660" width="700" height="160" class="section-bg" />
  <text x="400" y="680" class="section-title">5. DReG Loss Computation (dreg_looser)</text>

  <!-- Likelihood Components -->
  <rect x="80" y="700" width="120" height="50" class="loss-box" />
  <text x="140" y="720" class="text">Prior Log-Probs</text>
  <text x="140" y="735" class="small-text">log p(u), log p(w)</text>

  <rect x="220" y="700" width="120" height="50" class="loss-box" />
  <text x="280" y="720" class="text">Posterior Log-Probs</text>
  <text x="280" y="735" class="small-text">log q(u|x), log q(w|x)</text>

  <rect x="360" y="700" width="120" height="50" class="loss-box" />
  <text x="420" y="720" class="text">Reconstruction</text>
  <text x="420" y="735" class="small-text">log p(x|z) * rescale</text>

  <rect x="500" y="700" width="120" height="50" class="loss-box" />
  <text x="560" y="720" class="text">Importance Weights</text>
  <text x="560" y="735" class="small-text">wk for variance reduction</text>

  <!-- Total Likelihood -->
  <rect x="150" y="770" width="200" height="30" class="loss-box" />
  <text x="250" y="790" class="text">Total: lw = lpx_z + beta(lpz - lqu_x - lqw_x)</text>

  <!-- Gradient Hooks -->
  <rect x="370" y="770" width="150" height="30" class="loss-box" />
  <text x="445" y="790" class="text">Gradient Hooks: grad * wk</text>

  <!-- Section 6: Final Outputs -->
  <rect x="800" y="60" width="350" height="760" class="section-bg" />
  <text x="975" y="80" class="section-title">6. Final Outputs</text>

  <!-- Fusion Processing -->
  <rect x="820" y="100" width="140" height="40" class="output-box" />
  <text x="890" y="115" class="text">Shared Fusion</text>
  <text x="890" y="130" class="small-text">Average u across modalities</text>

  <rect x="980" y="100" width="140" height="40" class="output-box" />
  <text x="1050" y="115" class="text">Fusion Reconstructions</text>
  <text x="1050" y="130" class="small-text">Using averaged u + w_prior</text>

  <!-- Additional Losses -->
  <rect x="820" y="160" width="100" height="30" class="loss-box" />
  <text x="870" y="180" class="text">Subject Loss</text>

  <rect x="940" y="160" width="120" height="30" class="loss-box" />
  <text x="1000" y="180" class="text">Modality Pred Loss</text>

  <!-- Final ModelOutput -->
  <rect x="950" y="350" width="200" height="200" class="output-box" />
  <text x="1050" y="370" class="title">ModelOutput</text>
  <text x="1050" y="390" class="text">• zss: embeddings_share</text>
  <text x="1050" y="405" class="text">• recon: reconstructions</text>
  <text x="1050" y="420" class="text">• subj_loss: subject loss</text>
  <text x="1050" y="435" class="text">• brain_b: subject embeddings</text>
  <text x="1050" y="450" class="text">• modality_mu: private means</text>
  <text x="1050" y="465" class="text">• mod_predict_loss</text>
  <text x="1050" y="480" class="text">• mod_labels</text>
  <text x="1050" y="495" class="text">• embeddings: all latents</text>
  <text x="1050" y="510" class="text">• sample_k: True</text>
  <text x="1050" y="525" class="text">• loss: DReG loss</text>

  <!-- Arrows for loss computation -->
  <line x1="90" y1="490" x2="70" y2="560" class="arrow" />
  <line x1="180" y1="490" x2="180" y2="560" class="arrow" />
  <line x1="880" y1="200" x2="290" y2="560" class="dashed-arrow" />

  <!-- To total likelihood -->
  <line x1="70" y1="620" x2="150" y2="640" class="arrow" />
  <line x1="180" y1="620" x2="190" y2="640" class="arrow" />
  <line x1="290" y1="620" x2="230" y2="640" class="arrow" />

  <!-- To importance weighting -->
  <line x1="280" y1="660" x2="360" y2="590" class="arrow" />

  <!-- To fusion -->
  <line x1="880" y1="220" x2="660" y2="370" class="arrow" />
  <line x1="880" y1="220" x2="790" y2="370" class="arrow" />

  <!-- To final output -->
  <line x1="850" y1="400" x2="950" y2="450" class="arrow" />
  <line x1="570" y1="490" x2="950" y2="470" class="arrow" />
  <line x1="680" y1="490" x2="950" y2="480" class="arrow" />
  <line x1="420" y1="620" x2="950" y2="530" class="dashed-arrow" />

  <!-- Legend -->
  <text x="1200" y="60" class="title">Legend</text>
  <rect x="1180" y="70" width="20" height="15" class="input-box" />
  <text x="1210" y="82" class="text">Input Data</text>

  <rect x="1180" y="90" width="20" height="15" class="encoder-box" />
  <text x="1210" y="102" class="text">Encoders</text>

  <rect x="1180" y="110" width="20" height="15" class="latent-box" />
  <text x="1210" y="122" class="text">Latent Space</text>

  <rect x="1180" y="130" width="20" height="15" class="decoder-box" />
  <text x="1210" y="142" class="text">Decoders</text>

  <rect x="1180" y="150" width="20" height="15" class="loss-box" />
  <text x="1210" y="162" class="text">Loss Computation</text>

  <rect x="1180" y="170" width="20" height="15" class="output-box" />
  <text x="1210" y="182" class="text">Outputs</text>

  <rect x="1180" y="190" width="20" height="15" class="subject-box" />
  <text x="1210" y="202" class="text">Subject Processing</text>

  <line x1="1180" y1="210" x2="1200" y2="210" class="arrow" />
  <text x="1210" y="215" class="text">Data Flow</text>

  <line x1="1180" y1="225" x2="1200" y2="225" class="dashed-arrow" />
  <text x="1210" y="230" class="text">Control Flow</text>

</svg>
