<?xml version="1.0" encoding="UTF-8"?>
<svg width="1400" height="1000" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <style>
      .input-box { fill: #E3F2FD; stroke: #1976D2; stroke-width: 2; }
      .encoder-box { fill: #F3E5F5; stroke: #7B1FA2; stroke-width: 2; }
      .latent-box { fill: #FFF3E0; stroke: #F57C00; stroke-width: 2; }
      .decoder-box { fill: #E8F5E8; stroke: #388E3C; stroke-width: 2; }
      .loss-box { fill: #FFEBEE; stroke: #D32F2F; stroke-width: 2; }
      .output-box { fill: #F1F8E9; stroke: #689F38; stroke-width: 2; }
      .subject-box { fill: #FCE4EC; stroke: #C2185B; stroke-width: 2; }
      .text { font-family: Arial, sans-serif; font-size: 10px; text-anchor: middle; }
      .title { font-family: Arial, sans-serif; font-size: 12px; font-weight: bold; text-anchor: middle; }
      .arrow { stroke: #333; stroke-width: 2; fill: none; marker-end: url(#arrowhead); }
      .dashed-arrow { stroke: #666; stroke-width: 1.5; fill: none; stroke-dasharray: 5,5; marker-end: url(#arrowhead); }
    </style>
    <marker id="arrowhead" markerWidth="10" markerHeight="7" refX="9" refY="3.5" orient="auto">
      <polygon points="0 0, 10 3.5, 0 7" fill="#333" />
    </marker>
  </defs>

  <!-- Title -->
  <text x="700" y="25" class="title" font-size="16">MMVAEPlus_DIS Architecture - "dreg_looser" Objective</text>

  <!-- Input Layer -->
  <text x="100" y="60" class="title">Input Modalities</text>
  <rect x="20" y="70" width="60" height="40" class="input-box" />
  <text x="50" y="95" class="text">fMRI</text>

  <rect x="90" y="70" width="60" height="40" class="input-box" />
  <text x="120" y="95" class="text">EEG</text>

  <rect x="160" y="70" width="60" height="40" class="input-box" />
  <text x="190" y="95" class="text">Image</text>

  <rect x="230" y="70" width="60" height="40" class="input-box" />
  <text x="260" y="95" class="text">Text</text>

  <rect x="300" y="70" width="80" height="40" class="subject-box" />
  <text x="340" y="95" class="text">subj_index</text>

  <!-- Modality-Specific Encoders -->
  <text x="150" y="150" class="title">Modality-Specific Encoders</text>
  <rect x="20" y="160" width="60" height="30" class="encoder-box" />
  <text x="50" y="180" class="text">Enc_fMRI</text>

  <rect x="90" y="160" width="60" height="30" class="encoder-box" />
  <text x="120" y="180" class="text">Enc_EEG</text>

  <rect x="160" y="160" width="60" height="30" class="encoder-box" />
  <text x="190" y="180" class="text">Enc_Image</text>

  <rect x="230" y="160" width="60" height="30" class="encoder-box" />
  <text x="260" y="180" class="text">Enc_Text</text>

  <!-- Subject Encoder -->
  <rect x="300" y="160" width="80" height="30" class="subject-box" />
  <text x="340" y="180" class="text">Enc_Subject</text>

  <!-- Union Encoder -->
  <rect x="100" y="220" width="120" height="30" class="encoder-box" />
  <text x="160" y="240" class="text">Union Encoder</text>

  <!-- Embedding Splitting -->
  <text x="160" y="280" class="title">Embedding Splitting</text>
  <rect x="50" y="290" width="80" height="40" class="latent-box" />
  <text x="90" y="315" class="text">Shared (μ_u, σ_u)</text>

  <rect x="140" y="290" width="80" height="40" class="latent-box" />
  <text x="180" y="315" class="text">Private (μ_w, σ_w)</text>

  <rect x="230" y="290" width="80" height="40" class="latent-box" />
  <text x="270" y="315" class="text">Subject (μ_s, σ_s)</text>

  <!-- Subject Processing -->
  <rect x="400" y="220" width="100" height="60" class="subject-box" />
  <text x="450" y="240" class="text">Subject Processing</text>
  <text x="450" y="255" class="text">brain_b = (brain_b_ + sub_b)/2</text>
  <text x="450" y="270" class="text">brain_b_k = brain_b.unsqueeze(0)</text>

  <!-- Posterior Distributions -->
  <text x="180" y="370" class="title">Posterior Distributions (with DReG detachment)</text>
  <rect x="50" y="380" width="80" height="40" class="latent-box" />
  <text x="90" y="405" class="text">q_u(z_u|x)</text>

  <rect x="140" y="380" width="80" height="40" class="latent-box" />
  <text x="180" y="405" class="text">q_w(z_w|x)</text>

  <!-- K-Sampling -->
  <text x="180" y="450" class="title">K-Sampling</text>
  <rect x="50" y="460" width="80" height="30" class="latent-box" />
  <text x="90" y="480" class="text">u_x ~ q_u [K samples]</text>

  <rect x="140" y="460" width="80" height="30" class="latent-box" />
  <text x="180" y="480" class="text">w_x ~ q_w [K samples]</text>

  <!-- Reconstruction Paths -->
  <text x="600" y="150" class="title">Reconstruction Paths</text>

  <!-- Self-Reconstruction -->
  <rect x="520" y="170" width="160" height="50" class="decoder-box" />
  <text x="600" y="190" class="text">Self-Reconstruction</text>
  <text x="600" y="205" class="text">z = [u_x, w_x, brain_target]</text>

  <!-- Cross-Modal Reconstruction -->
  <rect x="520" y="240" width="160" height="50" class="decoder-box" />
  <text x="600" y="260" class="text">Cross-Modal Reconstruction</text>
  <text x="600" y="275" class="text">z = [u_x, w_prior, brain_target]</text>

  <!-- Union Decoder -->
  <rect x="720" y="200" width="100" height="30" class="decoder-box" />
  <text x="770" y="220" class="text">Union Decoder</text>

  <!-- Modality-Specific Decoders -->
  <rect x="850" y="160" width="60" height="30" class="decoder-box" />
  <text x="880" y="180" class="text">Dec_fMRI</text>

  <rect x="850" y="200" width="60" height="30" class="decoder-box" />
  <text x="880" y="220" class="text">Dec_EEG</text>

  <rect x="850" y="240" width="60" height="30" class="decoder-box" />
  <text x="880" y="260" class="text">Dec_Image</text>

  <!-- Arrows for main flow -->
  <line x1="50" y1="110" x2="50" y2="160" class="arrow" />
  <line x1="120" y1="110" x2="120" y2="160" class="arrow" />
  <line x1="190" y1="110" x2="190" y2="160" class="arrow" />
  <line x1="260" y1="110" x2="260" y2="160" class="arrow" />
  <line x1="340" y1="110" x2="340" y2="160" class="arrow" />

  <!-- To Union Encoder -->
  <line x1="50" y1="190" x2="120" y2="220" class="arrow" />
  <line x1="120" y1="190" x2="140" y2="220" class="arrow" />
  <line x1="190" y1="190" x2="180" y2="220" class="arrow" />
  <line x1="260" y1="190" x2="200" y2="220" class="arrow" />

  <!-- Subject processing -->
  <line x1="340" y1="190" x2="400" y2="220" class="arrow" />
  <line x1="120" y1="250" x2="400" y2="250" class="dashed-arrow" />

  <!-- To embedding splitting -->
  <line x1="160" y1="250" x2="90" y2="290" class="arrow" />
  <line x1="160" y1="250" x2="180" y2="290" class="arrow" />
  <line x1="450" y1="280" x2="270" y2="290" class="arrow" />

  <!-- To posteriors -->
  <line x1="90" y1="330" x2="90" y2="380" class="arrow" />
  <line x1="180" y1="330" x2="180" y2="380" class="arrow" />

  <!-- To sampling -->
  <line x1="90" y1="420" x2="90" y2="460" class="arrow" />
  <line x1="180" y1="420" x2="180" y2="460" class="arrow" />

  <!-- To reconstruction -->
  <line x1="180" y1="490" x2="520" y2="200" class="arrow" />
  <line x1="180" y1="490" x2="520" y2="270" class="arrow" />

  <!-- Through decoders -->
  <line x1="680" y1="200" x2="720" y2="215" class="arrow" />
  <line x1="680" y1="270" x2="720" y2="215" class="arrow" />
  <line x1="820" y1="215" x2="850" y2="180" class="arrow" />
  <line x1="820" y1="215" x2="850" y2="220" class="arrow" />
  <line x1="820" y1="215" x2="850" y2="260" class="arrow" />

  <!-- DReG Loss Computation -->
  <text x="180" y="540" class="title">DReG Loss Computation (dreg_looser)</text>

  <!-- Likelihood Components -->
  <rect x="20" y="560" width="100" height="60" class="loss-box" />
  <text x="70" y="580" class="text">Prior Log-Probs</text>
  <text x="70" y="595" class="text">log p(u), log p(w)</text>
  <text x="70" y="610" class="text">from mean_priors</text>

  <rect x="130" y="560" width="100" height="60" class="loss-box" />
  <text x="180" y="580" class="text">Posterior Log-Probs</text>
  <text x="180" y="595" class="text">log q(u|x), log q(w|x)</text>
  <text x="180" y="610" class="text">from detached posteriors</text>

  <rect x="240" y="560" width="100" height="60" class="loss-box" />
  <text x="290" y="580" class="text">Reconstruction</text>
  <text x="290" y="595" class="text">Log-Probs</text>
  <text x="290" y="610" class="text">log p(x|z) * rescale</text>

  <!-- Importance Weighting -->
  <rect x="360" y="560" width="120" height="60" class="loss-box" />
  <text x="420" y="580" class="text">Importance Weights</text>
  <text x="420" y="595" class="text">wk = exp(lw - logsumexp(lw))</text>
  <text x="420" y="610" class="text">for variance reduction</text>

  <!-- Total Likelihood -->
  <rect x="100" y="640" width="180" height="40" class="loss-box" />
  <text x="190" y="665" class="text">Total Likelihood: lw = lpx_z + beta(lpz - lqu_x - lqw_x)</text>

  <!-- Gradient Hooks -->
  <rect x="300" y="640" width="150" height="40" class="loss-box" />
  <text x="375" y="665" class="text">Gradient Hooks: grad * wk</text>

  <!-- Fusion and Final Outputs -->
  <text x="700" y="350" class="title">Fusion &amp; Final Outputs</text>

  <!-- Fusion Processing -->
  <rect x="600" y="370" width="120" height="50" class="output-box" />
  <text x="660" y="390" class="text">Shared Fusion</text>
  <text x="660" y="405" class="text">Average u across modalities</text>

  <rect x="730" y="370" width="120" height="50" class="output-box" />
  <text x="790" y="390" class="text">Fusion Reconstructions</text>
  <text x="790" y="405" class="text">Using averaged u + w_prior</text>

  <!-- Additional Losses -->
  <rect x="520" y="450" width="100" height="40" class="loss-box" />
  <text x="570" y="475" class="text">Subject Loss</text>

  <rect x="630" y="450" width="100" height="40" class="loss-box" />
  <text x="680" y="475" class="text">Modality Pred Loss</text>

  <!-- Final ModelOutput -->
  <rect x="950" y="350" width="200" height="200" class="output-box" />
  <text x="1050" y="370" class="title">ModelOutput</text>
  <text x="1050" y="390" class="text">• zss: embeddings_share</text>
  <text x="1050" y="405" class="text">• recon: reconstructions</text>
  <text x="1050" y="420" class="text">• subj_loss: subject loss</text>
  <text x="1050" y="435" class="text">• brain_b: subject embeddings</text>
  <text x="1050" y="450" class="text">• modality_mu: private means</text>
  <text x="1050" y="465" class="text">• mod_predict_loss</text>
  <text x="1050" y="480" class="text">• mod_labels</text>
  <text x="1050" y="495" class="text">• embeddings: all latents</text>
  <text x="1050" y="510" class="text">• sample_k: True</text>
  <text x="1050" y="525" class="text">• loss: DReG loss</text>

  <!-- Arrows for loss computation -->
  <line x1="90" y1="490" x2="70" y2="560" class="arrow" />
  <line x1="180" y1="490" x2="180" y2="560" class="arrow" />
  <line x1="880" y1="200" x2="290" y2="560" class="dashed-arrow" />

  <!-- To total likelihood -->
  <line x1="70" y1="620" x2="150" y2="640" class="arrow" />
  <line x1="180" y1="620" x2="190" y2="640" class="arrow" />
  <line x1="290" y1="620" x2="230" y2="640" class="arrow" />

  <!-- To importance weighting -->
  <line x1="280" y1="660" x2="360" y2="590" class="arrow" />

  <!-- To fusion -->
  <line x1="880" y1="220" x2="660" y2="370" class="arrow" />
  <line x1="880" y1="220" x2="790" y2="370" class="arrow" />

  <!-- To final output -->
  <line x1="850" y1="400" x2="950" y2="450" class="arrow" />
  <line x1="570" y1="490" x2="950" y2="470" class="arrow" />
  <line x1="680" y1="490" x2="950" y2="480" class="arrow" />
  <line x1="420" y1="620" x2="950" y2="530" class="dashed-arrow" />

  <!-- Legend -->
  <text x="1200" y="60" class="title">Legend</text>
  <rect x="1180" y="70" width="20" height="15" class="input-box" />
  <text x="1210" y="82" class="text">Input Data</text>

  <rect x="1180" y="90" width="20" height="15" class="encoder-box" />
  <text x="1210" y="102" class="text">Encoders</text>

  <rect x="1180" y="110" width="20" height="15" class="latent-box" />
  <text x="1210" y="122" class="text">Latent Space</text>

  <rect x="1180" y="130" width="20" height="15" class="decoder-box" />
  <text x="1210" y="142" class="text">Decoders</text>

  <rect x="1180" y="150" width="20" height="15" class="loss-box" />
  <text x="1210" y="162" class="text">Loss Computation</text>

  <rect x="1180" y="170" width="20" height="15" class="output-box" />
  <text x="1210" y="182" class="text">Outputs</text>

  <rect x="1180" y="190" width="20" height="15" class="subject-box" />
  <text x="1210" y="202" class="text">Subject Processing</text>

  <line x1="1180" y1="210" x2="1200" y2="210" class="arrow" />
  <text x="1210" y="215" class="text">Data Flow</text>

  <line x1="1180" y1="225" x2="1200" y2="225" class="dashed-arrow" />
  <text x="1210" y="230" class="text">Control Flow</text>

</svg>
