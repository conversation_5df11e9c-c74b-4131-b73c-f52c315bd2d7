import os
import torch
import numpy as np
from PIL import Image
from torch import nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import utils
import glob
import random


class NSDDataset(Dataset):
    def __init__(self, root_dir, extensions=None, pool_num=8192, pool_type="max", length=None, missing_prob=0.):
        self.root_dir = root_dir
        self.extensions = extensions if extensions else []
        self.pool_num = pool_num
        self.pool_type = pool_type
        self.samples = self._load_samples()
        self.samples_keys = sorted(self.samples.keys())
        self.length = length
        if length is not None:
            if length > len(self.samples_keys):
                pass # enlarge the dataset
            elif length > 0:
                self.samples_keys = self.samples_keys[:length]
            elif length < 0:
                self.samples_keys = self.samples_keys[length:]
            elif length == 0:
                raise ValueError("length must be a non-zero value!")
        else:
            self.length = len(self.samples_keys)
        
        self.size = self.length
        
        self.mode=root_dir.split('/')[-2]
        self.sub=int(root_dir.split("/")[-1].split("subj")[-1])
        self.missing_prob = missing_prob if self.sub == 7 else 0.0

        self.clip_list = glob.glob(os.path.join('/home/<USER>/nsd-fmri-eeg/fmri/', f'0{self.sub}', self.mode, '*.npz'))
        self.clip_list.sort()

        # self.clip_image_path = os.path.join('/'.join(root_dir.split('/')[:-3]), 'webdataset_avg_new', 'clip_image', f"{self.mode}-clip_image_{self.sub}.npy")
        # self.clip_text_path = os.path.join('/'.join(root_dir.split('/')[:-3]), 'webdataset_avg_new', 'clip_text', f"{self.mode}-clip_text_{self.sub}.npy")
        # self.clip_image_list = np.load(self.clip_image_path)
        # self.clip_text_list = np.load(self.clip_text_path)

    def _load_samples(self):
            files = os.listdir(self.root_dir)
            samples = {}
            for file in files:
                file_path = os.path.join(self.root_dir, file)
                sample_id, ext = file.split(".",maxsplit=1)
                if ext in self.extensions:
                    if sample_id in samples.keys():
                        samples[sample_id][ext] = file_path
                    else:
                        samples[sample_id]={"subj": file_path}
                        samples[sample_id][ext] = file_path
            return samples
    
    def _load_image(self, image_path):
        image = Image.open(image_path).convert('RGB')
        image = np.array(image).astype(np.float32) / 255.0
        image = torch.from_numpy(image.transpose(2, 0, 1))
        return image
    
    def _load_npy(self, npy_path):
        array = np.load(npy_path)
        array = torch.from_numpy(array)
        return array
    
    def vox_process(self, x):
        if self.pool_num is not None:
            x = pool_voxels(x, self.pool_num, self.pool_type)
        
        if self.mode == 'train':
            n = x.size(0)
            random_index = torch.randint(0, n, (1,))[0]
            x = x[random_index]
        else:
            x = torch.mean(x, axis=0)
        return x
    
    def subj_process(self, key):
        id = int(key.split("/")[-2].split("subj")[-1])
        return id
    
    def aug_process(self, brain3d):
        return brain3d

    def __len__(self):
        # return len(self.samples_keys)
        return self.length

    def __getitem__(self, idx):
        idx = idx % len(self.samples_keys)
        sample_key = self.samples_keys[idx]
        sample = self.samples[sample_key]
        items = {}

        # Decide whether to include fMRI for subject 8 (missing probability 0.5)
        include_fmri = True
        if self.sub == 7 and self.mode == 'train':
            include_fmri = torch.rand(1).item() >= self.missing_prob

        for ext in self.extensions:
            if ext == "jpg":
                items['jpg'] = self._load_image(sample[ext])
            elif ext == "nsdgeneral.npy":
                voxel = self._load_npy(sample[ext])
                items['fmri'] = self.vox_process(voxel)               
                # Set mask to 1 if including fMRI, 0 otherwise
                items['fmri_mask'] = torch.tensor(1.0 if include_fmri else 0.0)
            elif ext == "coco73k.npy":
                items['coco73k'] = self._load_npy(sample[ext])
                    
        items['subj'] = self.subj_process(sample['subj'])
        clip_data = np.load(self.clip_list[idx])
        items['image'] = clip_data['image']
        items['text'] = clip_data['text']
        return items

def pool_voxels(voxels, pool_num, pool_type):
    voxels = voxels.float()
    if pool_type == 'avg':
        voxels = nn.AdaptiveAvgPool1d(pool_num)(voxels)
    elif pool_type == 'max':
        voxels = nn.AdaptiveMaxPool1d(pool_num)(voxels)
    elif pool_type == "resize":
        voxels = voxels.unsqueeze(1) # Add a dimension to make it (B, 1, L)
        voxels = F.interpolate(voxels, size=pool_num, mode='linear', align_corners=False)
        voxels = voxels.squeeze(1)

    return voxels


def get_fmri_dataset(subject, data_path, pool_type, pool_num, length, seed, missing_prob):
    train_path = "/home/<USER>/nsd-fmri-eeg/webdataset_avg_new/train/subj0{}".format(subject)
    val_path = "/home/<USER>/nsd-fmri-eeg/webdataset_avg_new/test/subj0{}".format(subject)
    extensions = ['nsdgeneral.npy', "subj"]        

    utils.seed_everything(seed)

    if subject == 7:
        train_length = 8700 // 2
        val_length =  1000 // 2
    else:
        train_length = length
        val_length = length

    train_data = NSDDataset(
        train_path,
        extensions=extensions,
        pool_type=pool_type,
        pool_num=pool_num,
        missing_prob=missing_prob,
        length=train_length,
    )

    

    val_data = NSDDataset(
        val_path,
        extensions=extensions,
        pool_type=pool_type,
        pool_num=pool_num,
        length=val_length,
    )

    num_train=len(train_data)
    num_val=len(val_data)
    print(train_path,"\n",val_path)
    print(f"number of fmri_{subject} train data:", num_train)
    print(f"number of fmri_{subject} val data:", num_val)
    return train_data, val_data