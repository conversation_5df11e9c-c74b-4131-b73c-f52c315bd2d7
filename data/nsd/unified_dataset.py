import os
import torch
import numpy as np
from PIL import Image
from torch import nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import utils
import glob
import yaml
import json
from collections import defaultdict

class ModalityProcessor:
    """Base class for modality-specific processing"""
    def __init__(self):
        pass
    
    def process(self, data):
        raise NotImplementedError("Subclasses must implement process method")
    
    def get_paths(self):
        raise NotImplementedError("Subclasses must implement get_paths method")

class FMRIProcessor(ModalityProcessor):
    def __init__(self, subject, mode, pool_num=8192, pool_type="max", use_mean_feature=False):
        super().__init__()
        self.subject = subject
        self.mode = mode
        self.pool_num = pool_num
        self.pool_type = pool_type
        self.use_mean_feature = use_mean_feature
        
        # Setup paths
        self.root_dir = f"/home/<USER>/nsd-fmri-eeg/webdataset_avg_new/{mode}/subj0{subject}"
        
        if self.use_mean_feature:
            self.clip_dir = f"/home/<USER>/nsd-fmri-eeg/fmri_vit/0{subject}/{mode}"
        else:
            self.clip_dir = f"/home/<USER>/nsd-fmri-eeg/fmri/0{subject}/{mode}"
            
        # Load sample paths
        self.samples = self._load_samples()
        self.samples_keys = sorted(self.samples.keys())
        self.clip_list = glob.glob(os.path.join(self.clip_dir, '*.npz'))
        self.clip_list.sort()
    
    def _load_samples(self):
        files = os.listdir(self.root_dir)
        samples = {}
        for file in files:
            file_path = os.path.join(self.root_dir, file)
            sample_id, ext = file.split(".", maxsplit=1)
            if ext in ['nsdgeneral.npy', 'jpg', 'subj']:
                if sample_id in samples.keys():
                    samples[sample_id][ext] = file_path
                else:
                    samples[sample_id] = {"subj": file_path}
                    samples[sample_id][ext] = file_path
        return samples
    
    def _load_npy(self, npy_path):
        array = np.load(npy_path)
        array = torch.from_numpy(array)
        return array
    
    def pool_voxels(self, voxels):
        voxels = voxels.float()
        if self.pool_type == 'avg':
            voxels = nn.AdaptiveAvgPool1d(self.pool_num)(voxels)
        elif self.pool_type == 'max':
            voxels = nn.AdaptiveMaxPool1d(self.pool_num)(voxels)
        elif self.pool_type == "resize":
            voxels = voxels.unsqueeze(1)
            voxels = F.interpolate(voxels, size=self.pool_num, mode='linear', align_corners=False)
            voxels = voxels.squeeze(1)
        return voxels
    
    def vox_process(self, x):
        if self.pool_num is not None:
            x = self.pool_voxels(x)
        
        if self.mode == 'train':
            n = x.size(0)
            random_index = torch.randint(0, n, (1,))[0]
            x = x[random_index]
        else:
            x = torch.mean(x, axis=0)
        return x
    
    def get_paths(self):
        return self.samples_keys, self.clip_list
    
    def process(self, idx):
        sample_key = self.samples_keys[idx % len(self.samples_keys)]
        sample = self.samples[sample_key]
        clip_data = np.load(self.clip_list[idx % len(self.clip_list)])
        
        items = {}
        
        # Process fMRI data
        if 'nsdgeneral.npy' in sample:
            voxel = self._load_npy(sample['nsdgeneral.npy'])
            items['fmri'] = self.vox_process(voxel)
        
        # Process CLIP features
        items['image'] = clip_data['image']
        items['text'] = clip_data['text']
        
        if self.use_mean_feature:
            items['image'] = items['image'][0]
            items['text'] = items['text'][0]
            
        # Process subject ID
        items['subj'] = int(sample_key.split("/")[-2].split("subj")[-1]) if 'subj' in sample else self.subject
        
        return items

class EEGProcessor(ModalityProcessor):
    def __init__(self, subject, mode, eeg_size=8192, use_mean_feature=False, load_image=False):
        super().__init__()
        self.subject = int(subject)
        self.mode = mode
        self.eeg_size = eeg_size
        self.use_mean_feature = use_mean_feature
        self.load_image = load_image
        
        # Setup paths
        if self.use_mean_feature:
            self.eeg_dir = f"/home/<USER>/nsd-fmri-eeg/eeg_mean/0{self.subject}/{mode}"
        else:
            self.eeg_dir = f"/home/<USER>/nsd-fmri-eeg/eeg/0{self.subject}/{mode}"
            
        # Load sample paths
        if self.load_image:
            self.samples = self._load_samples()
            self.samples_keys = sorted(self.samples.keys())
            
        self.eeg_list = glob.glob(os.path.join(self.eeg_dir, '*.npz'))
        self.eeg_list.sort()
    
    def _load_samples(self):
        root_dir = f'/root/workspace/mindbridge/data/NSD/webdataset_avg_new/{self.mode}/subj01'
        files = os.listdir(root_dir)
        samples = defaultdict(dict)
        for file in files:
            file_path = os.path.join(root_dir, file)
            sample_id, ext = file.split(".", maxsplit=1)
            if ext == 'jpg':
                samples[sample_id][ext] = file_path
        return samples
    
    def _load_image(self, image_path):
        image = Image.open(image_path).convert('RGB')
        image = np.array(image).astype(np.float32) / 255.0
        image = torch.from_numpy(image.transpose(2, 0, 1))
        return image
    
    def vox_process(self, x):
        x = torch.mean(x.float(), axis=0)
        return x
    
    def get_paths(self):
        return self.eeg_list
    
    def process(self, idx):
        eeg_data = np.load(self.eeg_list[idx % len(self.eeg_list)])
        
        items = {}
        
        # Process EEG data
        x = torch.from_numpy(eeg_data['eeg'])
        items['eeg'] = self.vox_process(x)
        
        # Process CLIP features
        items['image'] = eeg_data['image'].astype(np.float32)
        items['text'] = eeg_data['text'].astype(np.float32)
        
        if self.use_mean_feature:
            items['image'] = items['image'][0]
            items['text'] = items['text'][0]
        
        # Process subject ID
        items['subj'] = self.subject
        
        # Load image if needed
        if self.load_image:
            sample_key = self.samples_keys[idx % len(self.samples_keys)]
            sample = self.samples[sample_key]
            items['jpg'] = self._load_image(sample['jpg'])
            
        return items

class UnifiedNSDDataset(Dataset):
    def __init__(self, modalities=None, length=None):
        """
        Unified dataset for loading different modalities
        
        Args:
            modalities: Dictionary of modality processors
                e.g., {'fmri_1': FMRIProcessor(...), 'eeg_5': EEGProcessor(...)}
            length: Optional dataset length override
        """
        super().__init__()
        self.modalities = modalities or {}
        
        # Determine dataset length
        self.lengths = {}
        for name, processor in self.modalities.items():
            if name.startswith('fmri'):
                keys, clips = processor.get_paths()
                self.lengths[name] = min(len(keys), len(clips))
            elif name.startswith('eeg'):
                self.lengths[name] = len(processor.get_paths())
        
        # Use provided length or max of all modalities
        if length is not None:
            self.length = length
        else:
            self.length = max(self.lengths.values()) if self.lengths else 0
        
        self.size = self.length
    
    def __len__(self):
        return self.length
    
    def __getitem__(self, idx):
        result = {}
        
        # Process each modality
        for name, processor in self.modalities.items():
            # Get modality-specific data
            modality_data = processor.process(idx)
            
            # Add to result with modality prefix to avoid key conflicts
            for k, v in modality_data.items():
                # Don't add prefix for subject ID and shared CLIP features
                if k in ['subj']:
                    if k not in result:  # Only add once
                        result[k] = v
                elif name.startswith('fmri') and k in ['image', 'text']:
                    result[f'fmri_{k}'] = v
                elif name.startswith('eeg') and k in ['image', 'text']:
                    result[f'eeg_{k}'] = v
                else:
                    result[k] = v
        
        return result

def get_unified_dataset(config):
    """
    Create unified datasets for training and validation
    
    Args:
        config: Dictionary containing configuration
            - modality_configs: List of modality configurations, each containing:
                - type: Modality type ('fmri' or 'eeg')
                - subjects: List of subjects to include
                - include_clip: Whether to include CLIP features (default: True)
            - pool_type: Pooling type for fMRI
            - pool_num: Number of pooled voxels for fMRI
            - eeg_size: Size of EEG features
            - length: Optional dataset length
            - seed: Random seed
            - use_mean_feature: Whether to use mean features
    """
    utils.seed_everything(config['seed'])
    
    train_modalities = {}
    val_modalities = {}
    
    # Process each modality configuration
    for modality_config in config['modality_configs']:
        modality_type = modality_config['type']
        subjects = modality_config['subjects']
        
        if modality_type == 'fmri':
            for subject in subjects:
                train_modalities[f'fmri_{subject}'] = FMRIProcessor(
                    subject=subject,
                    mode='train',
                    pool_type=config['pool_type'],
                    pool_num=config['pool_num'],
                    use_mean_feature=config['use_mean_feature']
                )
                
                val_modalities[f'fmri_{subject}'] = FMRIProcessor(
                    subject=subject,
                    mode='test',
                    pool_type=config['pool_type'],
                    pool_num=config['pool_num'],
                    use_mean_feature=config['use_mean_feature']
                )
        
        elif modality_type == 'eeg':
            for subject in subjects:
                train_modalities[f'eeg_{subject}'] = EEGProcessor(
                    subject=subject,
                    mode='train',
                    eeg_size=config['eeg_size'],
                    use_mean_feature=config['use_mean_feature'],
                    load_image=modality_config.get('load_image', False)
                )
                
                val_modalities[f'eeg_{subject}'] = EEGProcessor(
                    subject=subject,
                    mode='test',
                    eeg_size=config['eeg_size'],
                    use_mean_feature=config['use_mean_feature'],
                    load_image=modality_config.get('load_image', False)
                )
    
    # Create datasets
    train_dataset = UnifiedNSDDataset(
        modalities=train_modalities,
        length=config.get('length', None)
    )
    
    val_dataset = UnifiedNSDDataset(
        modalities=val_modalities
    )
    
    # Print dataset info
    print(f"Unified dataset created with modalities: {list(train_modalities.keys())}")
    print(f"Training dataset size: {len(train_dataset)}")
    print(f"Validation dataset size: {len(val_dataset)}")
    
    return train_dataset, val_dataset

def load_dataset_config(config_path):
    """
    Load dataset configuration from a YAML or JSON file
    
    Args:
        config_path: Path to the configuration file
        
    Returns:
        Dictionary containing the configuration
    """
    if not os.path.exists(config_path):
        raise FileNotFoundError(f"Config file not found: {config_path}")
    
    # Determine file type and load accordingly
    if config_path.endswith('.yaml') or config_path.endswith('.yml'):
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
    elif config_path.endswith('.json'):
        with open(config_path, 'r') as f:
            config = json.load(f)
    else:
        raise ValueError(f"Unsupported config file format: {config_path}")
    
    return config

def create_datasets_from_config(config_path, args):
    """
    Create datasets based on configuration file and command line arguments
    
    Args:
        config_path: Path to the configuration file
        args: Command line arguments containing global settings
        
    Returns:
        Dictionary mapping batch names to (train_dataset, val_dataset) tuples
    """
    # Load configuration
    config = load_dataset_config(config_path)
    
    if 'batches' not in config:
        raise ValueError("Configuration must contain 'batches' key")
    
    # Create datasets for each batch
    datasets = {}
    
    for batch_name, batch_config in config['batches'].items():
        if 'modality_configs' not in batch_config:
            raise ValueError(f"Batch '{batch_name}' must contain 'modality_configs' key")
        
        # Create a complete config by combining args and batch-specific settings
        complete_config = {
            'modality_configs': batch_config['modality_configs'],
            'pool_type': args.pool_type,
            'pool_num': args.pool_num,
            'eeg_size': args.eeg_size,
            'seed': args.seed,
            'use_mean_feature': args.use_mean_feature,
            'length': batch_config.get('length', args.length)
        }
        
        # Create datasets for this batch
        train_dataset, val_dataset = get_unified_dataset(complete_config)
        datasets[batch_name] = (train_dataset, val_dataset)
        
        print(f"Batch '{batch_name}' configuration:")
        for modality_config in batch_config['modality_configs']:
            print(f"  - {modality_config['type']}: {modality_config['subjects']}")
    
    return datasets
