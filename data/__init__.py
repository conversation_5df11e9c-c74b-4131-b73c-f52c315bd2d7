from .nsd.nsd_fmri_split import NSDDataset, get_fmri_dataset
from .nsd.nsd_eeg_split import NSDEEGDataset, get_eeg_dataset
from .nsd.unified_dataset import create_datasets_from_config

def get_dataset(name):
    if name == 'nsd':
        return {
            'fmri': get_fmri_dataset,
            'eeg': get_eeg_dataset,
            'unified': create_datasets_from_config
        }
    else:
        raise ValueError(f"Unknown dataset: {name}")

