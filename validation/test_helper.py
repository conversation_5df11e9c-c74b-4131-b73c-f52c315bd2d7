import torch
from torchvision import transforms
import numpy as np
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import confusion_matrix, f1_score, roc_auc_score
from scipy.stats import pearsonr
import matplotlib.pyplot as plt

knn = KNeighborsClassifier(n_neighbors=5, weights='distance')

import anndata as ad
import scib.metrics as me
import os
from os.path import join as pj
import scib

from collections import defaultdict
from validation.test_utils import *

class FileHandler:
    def __init__(self, filename):
        self.file = open(filename, 'w')

    def write(self, data):
        self.file.write(data)

    def __del__(self):
        if self.file:
            self.file.close()

class TestBase:
    def __init__(self, args, accelerator, voxel2clip, device) -> None:
        self.args = args
        self.accelerator = accelerator
        self.voxel2clip = voxel2clip
        self.device = device
        self.num_devices = max(torch.cuda.device_count(), 1)
        self.epoch_start = 0
        self.prepare_dataloader()
        self.prepare_multi_gpu()

        self.preproc = transforms.Compose([
            transforms.Resize(size=(224, 224), interpolation=transforms.InterpolationMode.BICUBIC, antialias=None),
            transforms.Normalize(mean=(0.48145466, 0.4578275, 0.40821073), std=(0.26862954, 0.26130258, 0.27577711))
        ])

        fmri_subj_tensor = torch.tensor(self.args.fmri_trainer_list).to(self.device)
        self.sorted_fmri_subj, _ = torch.sort(fmri_subj_tensor)
        eeg_subj_tensor = torch.tensor(self.args.eeg_trainer_list).to(self.device)
        self.sorted_eeg_subj, _ = torch.sort(eeg_subj_tensor)

        self.feature_image = []
        self.feature_text = []
        self.feature_align = []
        self.feature_fusion = []
        self.subj_b = []
        self.gt_img = []
        self.gt_txt = []

        if self.args.use_modality_specific_spaces:
            self.modality_image = []
            self.modality_text = []
            self.modality_align = []


        self.save_path = pj('./vis', self.args.exp_name)
        if not os.path.exists(self.save_path):
            os.makedirs(self.save_path)
        self.logger = FileHandler(f'./vis/{self.args.exp_name}/log.txt')
        self.get_sh()

    def get_sh(self):
        train_sh_path = 'scripts/test.sh'
            
        with open(train_sh_path, 'r') as train_file:
            train_content = train_file.read()

        self.logger.write("\n--- test.sh 文件内容 ---\n")
        self.logger.write(train_content)
        self.logger.write("\n--- test.sh 文件内容结束 ---\n\n")

        print(f"已成功将 {train_sh_path} 的内容复制")


    def prepare_multi_gpu(self):
        self.voxel2clip = self.accelerator.prepare(self.voxel2clip)

        for i, test_dl in enumerate(self.test_fmri_dls):
            self.test_fmri_dls[i] = self.accelerator.prepare(test_dl)
        
        for i, test_dl in enumerate(self.test_eeg_dls):
            self.test_eeg_dls[i] = self.accelerator.prepare(test_dl)

    def prepare_dataloader(self):
        pass

    
    def load(self,):
        print("\n---load from ckpt: {}---\n".format(self.args.load_from))
        checkpoint = torch.load(self.args.load_from, map_location='cpu')

        filtered_dict = {k: v for k, v in checkpoint['model_state_dict'].items() if "discriminator" not in k and "classify" not in k}
        self.voxel2clip.load_state_dict(filtered_dict, strict=False)

        #self.voxel2clip.load_state_dict(checkpoint['model_state_dict'], strict=False)
        print("loaded keys", checkpoint['model_state_dict'].keys())
        del checkpoint

    def test(self, local_rank):

        self.voxel2clip.eval()

        self.sims_image = 0.
        self.sims_text = 0.
        self.val_sims_text = 0.
        self.fwd_percent_correct = 0.
        self.bwd_percent_correct = 0.
        self.val_sims_image_1 = 0.
        self.val_fwd_percent_correct_1 = 0.
        self.val_bwd_percent_correct_1 = 0.
        self.val_sims_image_2 = 0.
        self.val_fwd_percent_correct_2 = 0.
        self.val_bwd_percent_correct_2 = 0.
        self.val_sims_image_5 = 0.
        self.val_fwd_percent_correct_5 = 0.
        self.val_bwd_percent_correct_5 = 0.
        self.val_sims_image_7 = 0.
        self.val_fwd_percent_correct_7 = 0.
        self.val_bwd_percent_correct_7 = 0.
        self.loss_clip_image_sum = 0.
        self.val_loss_clip_image_sum = 0.
        self.val_loss_clip_text_sum = 0.
        self.val_loss_mse_image_sum = 0.
        self.val_loss_mse_text_sum = 0.
        self.val_loss_rec_sum = 0.
        self.val_loss_cyc_sum = 0.

        self.test_epoch()
        #self.log_val()

        # if local_rank==0:
        #     print(self.logs)
        self.accelerator.wait_for_everyone()

    def log_val(self):
        self.logs={
            "val/cosine_sim_image_1": self.val_sims_image_1 / (self.val_i + 1),
            "val/val_fwd_pct_correct_1": self.val_fwd_percent_correct_1 / (self.val_i + 1),
            "val/val_bwd_pct_correct_1": self.val_bwd_percent_correct_1 / (self.val_i + 1),
            "val/cosine_sim_image_2": self.val_sims_image_2 / (self.val_i + 1),
            "val/val_fwd_pct_correct_2": self.val_fwd_percent_correct_2 / (self.val_i + 1),
            "val/val_bwd_pct_correct_2": self.val_bwd_percent_correct_2 / (self.val_i + 1),
            "val/cosine_sim_image_5": self.val_sims_image_5 / (self.val_i + 1),
            "val/val_fwd_pct_correct_5": self.val_fwd_percent_correct_5 / (self.val_i + 1),
            "val/val_bwd_pct_correct_5": self.val_bwd_percent_correct_5 / (self.val_i + 1),
            "val/cosine_sim_image_7": self.val_sims_image_7 / (self.val_i + 1),
            "val/val_fwd_pct_correct_7": self.val_fwd_percent_correct_7 / (self.val_i + 1),
            "val/val_bwd_pct_correct_7": self.val_bwd_percent_correct_7 / (self.val_i + 1),
            "val/loss_clip_image": self.val_loss_clip_image_sum / (self.val_i + 1),
            "val/loss_clip_text": self.val_loss_clip_text_sum / (self.val_i + 1),
            "val/loss_mse_image": self.val_loss_mse_image_sum / (self.val_i + 1),
            "val/loss_mse_text": self.val_loss_mse_text_sum / (self.val_i + 1),
            "val/loss_rec": self.val_loss_rec_sum / (self.val_i + 1),
            "val/loss_cyc": self.val_loss_cyc_sum / (self.val_i + 1),
        }

    def get_inference(self, dls, brain_type):
        if self.args.model_name == 'MIDAS':
            return self.get_inference_dis(dls, brain_type)
        elif '_DIS' in self.args.model_name:
            return self.get_inference_bl_dis(dls, brain_type)
        else:
            return self.get_inference_bl(dls, brain_type)

    def get_inference_bl_dis(self, dls, brain_type):
        gt_voxel = []
        subj_gt = []
        trans = defaultdict(list)
        keys = [brain_type, 'image', 'text', 'fusion']

        for i, test_dl in enumerate(dls):
            for train_i, data_i in enumerate(test_dl):
                voxel = data_i[f'{brain_type}']

                subj_id = data_i['subj']
                if 'fmri' in data_i.keys():
                    subj_index = torch.searchsorted(self.sorted_fmri_subj, subj_id)
                if 'eeg' in data_i.keys():
                    subj_index = torch.searchsorted(self.sorted_eeg_subj, subj_id)+len(self.args.fmri_trainer_list)
                data_i['subj_index'] = subj_index

                result = self.voxel2clip.forward(data_i)
                for src in keys:
                    for target in keys:
                        if target != 'fusion':
                            if src in ['image', 'text'] and target in ['image', 'text'] and i!=0:
                                continue

                            rec = result['recon'][f'{src}'][f'{target}'].detach().cpu().numpy()
                            if result['sample_k']:
                                rec = rec[0]
                            trans[f'{src}2{target}'].append(rec)

                
                image_feat = result['zss']['image'].detach().cpu().numpy()
                text_feat = result['zss']['text'].detach().cpu().numpy()
                brain_feat = result['zss'][f'{brain_type}'].detach().cpu().numpy()
                fusion_feat = result['zss']['fusion'].detach().cpu().numpy()

                if result['sample_k']:
                    image_feat = image_feat[0]
                    text_feat = text_feat[0]
                    brain_feat = brain_feat[0]
                    fusion_feat = fusion_feat[0]

                if i==0:    
                    self.feature_image.append(image_feat)
                    self.feature_text.append(text_feat)
                self.subj_b.append(result['brain_b'].detach().cpu().numpy())
                subj_gt.append(data_i['subj'].detach().cpu().numpy())
                self.feature_align.append(brain_feat)
                self.feature_fusion.append(fusion_feat)
                gt_voxel.append(voxel.detach().cpu().numpy())

                if self.args.use_modality_specific_spaces:
                    print('image shape', result['modality_mu']['image'].shape)
                    print('text shape', result['modality_mu']['text'].shape)
                    print('brain shape', result['modality_mu'][f'{brain_type}'].shape)
                    self.modality_image.append(result['modality_mu']['image'].detach().cpu().numpy())
                    self.modality_text.append(result['modality_mu']['text'].detach().cpu().numpy())
                    self.modality_align.append(result['modality_mu'][f'{brain_type}'].detach().cpu().numpy())

        for key, value in trans.items():
            trans[key] = np.concatenate(value)        
        gt_voxel = np.concatenate(gt_voxel)
        subj_gt = np.concatenate(subj_gt)
        
        return trans, gt_voxel, subj_gt


    def get_inference_bl(self, dls, brain_type):
        gt_voxel = []
        subj_gt = []
        trans = defaultdict(list)
        keys = [brain_type, 'image', 'text', 'fusion']

        for i, test_dl in enumerate(dls):
            for train_i, data_i in enumerate(test_dl):
                voxel = data_i[f'{brain_type}']
                result = self.voxel2clip.forward(data_i)

                for src in keys:
                    for target in keys:
                        if target != 'fusion':
                            if src in ['image', 'text'] and target in ['image', 'text'] and i!=0:
                                continue

                            rec = result['recon'][f'{src}'][f'{target}'].detach().cpu().numpy()
                            if result['sample_k']:
                                rec = rec[0]
                            trans[f'{src}2{target}'].append(rec)

                
                image_feat = result['zss']['image'].detach().cpu().numpy()
                text_feat = result['zss']['text'].detach().cpu().numpy()
                brain_feat = result['zss'][f'{brain_type}'].detach().cpu().numpy()
                fusion_feat = result['zss']['fusion'].detach().cpu().numpy()

                if result['sample_k']:
                    image_feat = image_feat[0]
                    text_feat = text_feat[0]
                    brain_feat = brain_feat[0]
                    fusion_feat = fusion_feat[0]

                if i==0:    
                    self.feature_image.append(image_feat)
                    self.feature_text.append(text_feat)
                self.subj_b.append([0])
                subj_gt.append(data_i['subj'].detach().cpu().numpy())
                self.feature_align.append(brain_feat)
                self.feature_fusion.append(fusion_feat)
                gt_voxel.append(voxel.detach().cpu().numpy())

        for key, value in trans.items():
            trans[key] = np.concatenate(value)        
        gt_voxel = np.concatenate(gt_voxel)
        subj_gt = np.concatenate(subj_gt)
        
        return trans, gt_voxel, subj_gt

    def get_inference_dis(self, dls, brain_type):
        gt_voxel = []
        subj_gt = []
        trans = defaultdict(list)
        keys = [brain_type, 'image', 'text', 'fusion']

        for i, test_dl in enumerate(dls):
            for train_i, data_i in enumerate(test_dl):
                voxel = data_i[f'{brain_type}']

                subj_id = data_i['subj']
                if 'fmri' in data_i.keys():
                    subj_index = torch.searchsorted(self.sorted_fmri_subj, subj_id)
                if 'eeg' in data_i.keys():
                    subj_index = torch.searchsorted(self.sorted_eeg_subj, subj_id)+len(self.args.fmri_trainer_list)
                data_i['subj_index'] = subj_index

                result = self.voxel2clip.forward(data_i)

                for src in keys:
                    for target in keys:
                        if target != 'fusion':
                            if src in ['image', 'text'] and target in ['image', 'text'] and i!=0:
                                continue
                            trans[f'{src}2{target}'].append(result['output'][f'{src}2{target}'].detach().cpu().numpy())

                if i==0:    
                    self.feature_image.append(result['emb_dict']['image']['c'].detach().cpu().numpy())
                    self.feature_text.append(result['emb_dict']['text']['c'].detach().cpu().numpy())
                    
                gt_voxel.append(voxel.detach().cpu().numpy())
                self.feature_align.append(result['emb_dict'][f'{brain_type}']['c'].detach().cpu().numpy())
                #subj_b.append(result['emb_dict'][f'{brain_type}']['b'].detach().cpu().numpy())
                self.subj_b.append(result['brain_b'].detach().cpu().numpy())
                subj_gt.append(data_i['subj'].detach().cpu().numpy())
                self.feature_fusion.append(result['emb_dict']['fusion']['c'].detach().cpu().numpy())
                
        for key, value in trans.items():
            trans[key] = np.concatenate(value)
            #print(key, np.concatenate(value).shape)
        
        gt_voxel = np.concatenate(gt_voxel)
        subj_gt = np.concatenate(subj_gt)
        
        return trans, gt_voxel, subj_gt

    def batch_score(self, c, num, subj_gt):
        embed = "X_emb"
        batch_key = "batch"
        label_key = "label"
        cluster_key = "cluster"
        output_type = "embed"
        subsample = 0.5
        verbose = False

        clusters_idx = np.array([i for cluster in self.clusters for i in cluster])
        split_c = []
        for i in range(num):
            start = i * 1000
            end = (i + 1) * 1000
            split_c.append(c[start:end, :])

        sorted_split_c = [i[clusters_idx] for i in split_c]
        sorted_c = np.vstack(sorted_split_c)
        print('sorted_c', sorted_c.shape)

        label = []
        for i, cluster in enumerate(self.clusters):
            label.extend([i]*len(cluster))
        label = np.array(label)
        label_cat = np.tile(label, num)

        adata = ad.AnnData(sorted_c)
        adata.obsm[embed] = sorted_c
        adata.obs[batch_key] = subj_gt.astype(str)
        adata.obs[batch_key] = adata.obs[batch_key].astype("category")
        adata.obs[label_key] = label_cat
        adata.obs[label_key] = adata.obs[label_key].astype("category")

        results = {}
        print('clustering...')
        res_max, nmi_max, nmi_all = scib.clustering.opt_louvain(adata, label_key=label_key,
            cluster_key=cluster_key, function=me.nmi, use_rep=embed, verbose=verbose, inplace=True)

        results['NMI'] = me.nmi(adata, cluster_key=cluster_key, label_key=label_key, implementation='arithmetic')
        print("NMI: " + str(results['NMI']))
        self.logger.write("\nNMI: " + str(results['NMI'])+'\n')

        results['ARI'] = me.ari(adata, cluster_key=cluster_key, label_key=label_key)
        print("ARI: " + str(results['ARI']))
        self.logger.write("ARI: " + str(results['ARI'])+'\n')

        type_ = "knn" if output_type == "graph" else None
        results['kBET'] = me.kBET(adata, batch_key=batch_key, label_key=label_key, embed=embed, 
            type_=type_, verbose=verbose)
        print("kBET: " + str(results['kBET']))
        self.logger.write("kBET: " + str(results['kBET'])+'\n')

        results['il_score_f1'] = me.isolated_labels(adata, label_key=label_key, batch_key=batch_key,
            embed=embed, cluster=True, verbose=verbose)
        print("il_score_f1: " + str(results['il_score_f1']))
        self.logger.write("il_score_f1: " + str(results['il_score_f1'])+'\n')

        results['graph_conn'] = me.graph_connectivity(adata, label_key=label_key)
        print("graph_conn: " + str(results['graph_conn']))
        self.logger.write("graph_conn: " + str(results['graph_conn'])+'\n')

        results['cLISI'] = me.clisi_graph(adata, batch_key=batch_key, label_key=label_key, type_="knn",
            subsample=subsample*100, n_cores=1, verbose=verbose)
        print("cLISI: " + str(results['cLISI']))
        self.logger.write("cLISI: " + str(results['cLISI'])+'\n')

        results['iLISI'] = me.ilisi_graph(adata, batch_key=batch_key, type_="knn",
            subsample=subsample*100, n_cores=1, verbose=verbose)
        print("iLISI: " + str(results['iLISI']))
        self.logger.write("iLISI: " + str(results['iLISI'])+'\n')
    
    def metirc_function(self, val1, val2, combo):
        # metric
        embed = "X_emb"
        label_key = "label"
        mod_key = "modality"
        si_metric = "euclidean"
        verbose = False

        #clusters = [label0, label4, label10, label16, label17, label18]
        clusters_idx = np.array([i for cluster in self.clusters for i in cluster])
        val1_selection = val1[clusters_idx]
        val2_selection = val2[clusters_idx]
        label = []
        for i, cluster in enumerate(self.clusters):
            label.extend([i]*len(cluster))
        label = np.array(label)
        label_cat = np.tile(label, 2)
        #print(val1_selection.shape, val2_selection.shape, label.shape)

        c_cat = np.concatenate((val1_selection, val2_selection), axis=0)
        mods_cat = ["image"]*len(val1_selection) + ["text"]*len(val2_selection)

        adata = ad.AnnData(c_cat)
        adata.obsm[embed] = c_cat
        adata.obs[mod_key] = mods_cat
        adata.obs[mod_key] = adata.obs[mod_key].astype("category")
        adata.obs[label_key] = label_cat
        adata.obs[label_key] = adata.obs[label_key].astype("category")
        asw_mod = me.silhouette_batch(adata, batch_key=mod_key,
            label_key=label_key, embed=embed, metric=si_metric, verbose=verbose)
        print(f'{combo[0]}->{combo[1]}  asw_mod', asw_mod)
        self.logger.write(f'{combo[0]}->{combo[1]}  asw_mod, {asw_mod} \n')

        # #-------------------------------------
        val1 = torch.tensor(val1)
        val2 = torch.tensor(val2)
        #print(feature_image.shape, feature_text.shape)
        foscttm = 1-calc_foscttm(val1, val2)
        print(f'{combo[0]}->{combo[1]}  foscttm', foscttm)
        self.logger.write(f'{combo[0]}->{combo[1]}  foscttm, {foscttm} \n')
        #---------------------------------------

        knn.fit(val1_selection, label)
        label_pred = knn.predict(val2_selection)
        # cm = confusion_matrix(label, label_pred, labels=knn.classes_)
        f1 = f1_score(label, label_pred, average='micro')
        print(f'{combo[0]}->{combo[1]}  f1', f1)
        self.logger.write(f'{combo[0]}->{combo[1]}  f1, {f1} \n')
        #-----------------------------------------
    

    def trans_func(self, trans, gt_voxel, brain_type, num_sub):
        for key, value in trans.items():
            source, target = key.split('2')
            print(target, key)
            if target == 'image':
                x_gt = np.concatenate(self.gt_img)
            elif target == 'text':
                x_gt = np.concatenate(self.gt_txt)
            elif target == f'{brain_type}':
                x_gt = gt_voxel

            print(key, x_gt.shape, value.shape)
            if (source == f'{brain_type}' and target != f'{brain_type}') or (source == 'fusion' and target != f'{brain_type}'):
                part_size = value.shape[0] // num_sub
                for i in range(num_sub):
                    value_i = value[i*part_size:(i+1)*part_size, :]

                    x_gt_flat = x_gt.flatten()
                    x_pred_flat = value_i.flatten()
                    p = pearsonr(x_gt_flat, x_pred_flat)[0]
                    print(key+f'_{i}', p)
                    self.logger.write(f'{key}_{i} {p} \n')
                self.logger.write('-'*10+'\n')
            
            elif target==f'{brain_type}':
                part_size = value.shape[0] // num_sub
                for i in range(num_sub):
                    value_i = value[i*part_size:(i+1)*part_size, :]
                    x_gt_i = x_gt[i*part_size:(i+1)*part_size, :]
                    x_gt_i_flat = x_gt_i.flatten()
                    x_pred_flat = value_i.flatten()
                    p = pearsonr(x_gt_i_flat, x_pred_flat)[0]
                    print(key+f'_{i}', p)
                    self.logger.write(f'{key}_{i} {p} \n')
                self.logger.write('-'*10+'\n')
                    
            # else:
            #     x_gt_flat = x_gt.flatten()
            #     x_pred_flat = value.flatten()
            #     p = pearsonr(x_gt_flat, x_pred_flat)[0]
            #     print(key, p)
            #     self.logger.write(f'{key} {p} \n')
            #     self.logger.write('-'*10+'\n')

    def test_epoch(self):
        import matplotlib.pyplot as plt
        from matplotlib.colors import ListedColormap

        print("Evaluating...")
        self.voxel2clip.eval()

        #--------------------------------
        for train_i, data_i in enumerate(self.test_fmri_dls[0]):
            clip_img, clip_txt = data_i['image'], data_i['text']
            self.gt_img.append(clip_img.flatten(1).detach().cpu().numpy())
            self.gt_txt.append(clip_txt.flatten(1).detach().cpu().numpy())

        if 'fmri' in self.args.use_modalities: 
            trans_fmri, gt_fmri, subj_fmri = self.get_inference(self.test_fmri_dls, 'fmri')
        if 'eeg' in self.args.use_modalities: 
            trans_eeg, gt_eeg, subj_eeg = self.get_inference(self.test_eeg_dls, 'eeg')
        feature_image = np.concatenate(self.feature_image)[:1000]
        feature_text = np.concatenate(self.feature_text)[:1000]
        feature_align = np.concatenate(self.feature_align)
        feature_fusion = np.concatenate(self.feature_fusion)

        num_fmri = len(self.args.fmri_subj_list)
        num_eeg = len(self.args.eeg_subj_list)
        feature_fmri = feature_align[:1000*num_fmri]
        feature_eeg = feature_align[1000*num_fmri:]
        print('feature fmri', feature_fmri.shape)
        print('feature eeg', feature_eeg.shape)

        self.batch_score(feature_fmri, num_fmri, subj_fmri)
        self.batch_score(feature_eeg, num_eeg, subj_eeg)
        self.logger.write('\n')

        # print(feature_image.shape)
        # print(feature_text.shape)
        # print(feature_align.shape)
        # print(subj_b.shape)

        #------------------------------------
        feature_dict = {
            'feature_image': feature_image,
            'feature_text': feature_text,
            'feature_align': feature_align
        }

        num_sub_fmri = len(self.test_fmri_dls)        
        num_sub = len(self.test_fmri_dls) + len(self.test_eeg_dls)
        keys = list(feature_dict.keys())
        #--------------------------------------
        import itertools

        self.logger.write('\n----------align---------------\n')
        # 使用itertools.combinations生成所有两两组合
        for combo in itertools.combinations(keys, 2):
            combo = list(combo)
            # 获取每一对组合的值
            val1 = feature_dict[combo[0]]
            val2 = feature_dict[combo[1]]
 
            if combo[0] == 'feature_align':
                part_size = val1.shape[0] // num_sub
                for i in range(num_sub):
                    val1_i = val1[i*part_size:(i+1)*part_size, :]
                    combo_ = combo.copy()
                    combo_[0] = combo_[0]+f'_{i}'
                    self.metirc_function(val1_i, val2, combo_)
                    self.logger.write('-'*10+'\n')
            
            elif combo[1] == 'feature_align':
                part_size = val2.shape[0] // num_sub
                for i in range(num_sub):
                    val2_i = val2[i*part_size:(i+1)*part_size, :]
                    combo_ = combo.copy()
                    combo_[1] = combo_[1]+f'_{i}'
                    self.metirc_function(val1, val2_i, combo_)
                    self.logger.write('-'*10+'\n')
            
            else:
                self.metirc_function(val1, val2, combo)
                self.logger.write('-'*10+'\n')


        #---------------------------------------
        self.logger.write('\n----------pearsonr---------------\n')
        if 'fmri' in self.args.use_modalities:
            self.logger.write('----------fmri---------------\n')
            self.trans_func(trans_fmri, gt_fmri, 'fmri', len(self.test_fmri_dls))
        if 'eeg' in self.args.use_modalities:
            self.logger.write('----------eeg---------------\n') 
            self.trans_func(trans_eeg, gt_eeg, 'eeg', len(self.test_eeg_dls))

        #----------------------------------------
        # print(trans_fmri['image2fmri'].shape)
        # print(gt_fmri.shape)
        # np.save('visualize_loc/fmri/image2fmri.npy', trans_fmri['image2fmri'])
        # np.save('visualize_loc/fmri/gt_fmri.npy', gt_fmri)

        # #---------------------------------------

        from sklearn.decomposition import PCA
        import umap

        pca = PCA(n_components=50)
        umap_reducer = umap.UMAP(n_components=2, random_state=42)

        #--------------------------m-----------------------------
        if self.args.use_modality_specific_spaces:

            m_list = ['image', 'text']
            for item in range(len(self.args.fmri_subj_list)):
                m_list.append(f'fmri{self.args.fmri_subj_list[item]}')
            for item in range(len(self.args.eeg_subj_list)):
                m_list.append(f'eeg{self.args.eeg_subj_list[item]}')
            
            modality_image = np.concatenate(self.modality_image)[:1000]
            modality_text = np.concatenate(self.modality_text)[:1000]
            modality_align = np.concatenate(self.modality_align)

            modality_mu = np.concatenate([modality_image, modality_text, modality_align])
            umap_mu = umap_reducer.fit_transform(modality_mu)
            num_per_sub = umap_mu.shape[0] // len(m_list)

            for label in range(len(m_list)):
                color = label_to_color[label+1]

                plt.scatter(umap_mu[:, 0], umap_mu[:, 1], 
                        color='lightgrey', label='Other modality')
                
                plt.scatter(umap_mu[label*num_per_sub:(label+1)*num_per_sub, 0], 
                            umap_mu[label*num_per_sub:(label+1)*num_per_sub, 1], 
                            c=color, label=f'modality: {m_list[label]}')
                plt.savefig(pj(self.save_path, f'modality_{m_list[label]}.png'))
                plt.clf()

        #--------------------------b-----------------------------
        if self.args.model_name == 'MIDAS' or '_DIS' in self.args.model_name:
            subj_b = np.concatenate(self.subj_b)
            umap_b = umap_reducer.fit_transform(subj_b)
            print('subj_b', subj_b.shape)
            num_per_sub = umap_b.shape[0] // num_sub

            for label in range(num_sub):
                color = label_to_color[label+1]
                plt.scatter(umap_b[:, 0], umap_b[:, 1], 
                        color='lightgrey', label='Other subj')
                if label < len(self.test_fmri_dls):
                    plt.scatter(umap_b[label*num_per_sub:(label+1)*num_per_sub, 0], 
                                        umap_b[label*num_per_sub:(label+1)*num_per_sub, 1], 
                                        c=color, label=f'fmri subj {self.args.fmri_subj_list[label]}')
                # else:
                #     plt.scatter(umap_b[label*num_per_sub:(label+1)*num_per_sub, 0], 
                #                 umap_b[label*num_per_sub:(label+1)*num_per_sub, 1], 
                #                 c=color, label=f'eeg subj {self.args.eeg_subj_list[label-num_sub_fmri]}')

            #plt.legend()
                    plt.savefig(pj(self.save_path, f'fmri_subj{self.args.fmri_subj_list[label]}.png'))
                    plt.clf()
                else:
                    plt.scatter(umap_b[label*num_per_sub:(label+1)*num_per_sub, 0], 
                            umap_b[label*num_per_sub:(label+1)*num_per_sub, 1], 
                            c=color, label=f'eeg subj {self.args.eeg_subj_list[label-num_sub_fmri]}')
                    plt.savefig(pj(self.save_path, f'eeg_subj{self.args.eeg_subj_list[label-num_sub_fmri]}.png'))
                    plt.clf()


            # for label in range(num_sub):
            #         color = label_to_color[label+1]
            #         if label < len(self.test_fmri_dls):
            #             continue
            #         else:
            #             plt.scatter(umap_b[label*num_per_sub:(label+1)*num_per_sub, 0], 
            #                         umap_b[label*num_per_sub:(label+1)*num_per_sub, 1], 
            #                         c=color, label=f'eeg subj {self.args.eeg_subj_list[label-num_sub_fmri]}')
            # plt.savefig(pj(self.save_path, 'eeg_subj.png'))
            # plt.clf()

        #--------------------------c-------------------------------

        feature_all = np.concatenate([feature_align, feature_image, feature_text, feature_fusion], 0)
        print('feature all:', feature_all.shape)
        pca_all = pca.fit_transform(feature_all)
        all_umap = umap_reducer.fit_transform(pca_all)

        def plot_umap_by_modality(umap_data, title, active_modality_index, num_samples_per_modality):
            #print('active', active_modality_index)
            plt.figure(figsize=(10, 8))
            is_active_modality = (np.arange(umap_data.shape[0]) // num_samples_per_modality) == active_modality_index
            
            plt.scatter(umap_data[~is_active_modality, 0], umap_data[~is_active_modality, 1], 
                        color='lightgrey', label='Other modality')
            
            umap_data_active = umap_data[is_active_modality]

            for i, cluster in enumerate(self.clusters):
                plt.scatter(umap_data_active[cluster, 0], 
                            umap_data_active[cluster, 1], 
                            c=label_to_color[i+1], label=f'cluster {i}')
    
            plt.title(title)
            #plt.legend()
            plt.savefig(pj(self.save_path, f'{title}.png'))

        num_samples_per_modality = 1000
        for item in range(len(self.test_fmri_dls)):
            plot_umap_by_modality(all_umap, f'fmri{self.args.fmri_subj_list[item]}', item, num_samples_per_modality)
        for item in range(len(self.test_eeg_dls)):
            plot_umap_by_modality(all_umap, f'eeg{self.args.eeg_subj_list[item]}', item+num_sub_fmri, num_samples_per_modality)
        plot_umap_by_modality(all_umap, 'image', num_sub, num_samples_per_modality)
        plot_umap_by_modality(all_umap, 'text', num_sub+1, num_samples_per_modality)
        for item in range(num_sub):
            plot_umap_by_modality(all_umap, f'joint{item}', num_sub+2+item, num_samples_per_modality)