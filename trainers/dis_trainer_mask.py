import torch
import torch.nn as nn
from .base_trainer import Trainer
from data.sampler import MultiDatasetSampler
import utils

class Trainer_bridge(Trainer):
    def __init__(self, args, accelerator, voxel2clip, device, data) -> None:
        super().__init__(args, accelerator, voxel2clip, device, data)

        self.prepare_optimizer_dis()


    def prepare_optimizer_dis(self,):
        self.optimizer_dis = torch.optim.AdamW(self.voxel2clip.discriminator.parameters(), lr=self.args.max_lr)

    def prepare_dataloader(self):
        # Prepare data and dataloader
        print("Preparing data and dataloader...")
        self.train_datasets = [] # tarin_dls contains all subjects separately
        self.val_datasets = [] # tarin_dls contains all subjects separately

        if 'fmri' in self.args.use_modalities:
            for subj in self.args.fmri_subj_list:
                train_data, val_data = self.data['fmri'](
                    subject=subj,
                    data_path=self.args.data_path,
                    pool_type=self.args.pool_type,
                    pool_num=self.args.pool_num,
                    length=self.args.length,
                    seed=self.args.seed,
                    missing_prob=self.args.missing_prob,
                )
                self.train_datasets.append(train_data)
                self.val_datasets.append(val_data)   

        if 'eeg' in self.args.use_modalities:
            for subj in self.args.eeg_subj_list:
                train_data, val_data = self.data['eeg'](
                    subject=subj,
                    data_path=self.args.data_path,
                    eeg_size=self.args.eeg_size,
                    length=self.args.length,
                    seed=self.args.seed,
                    missing_prob=self.args.missing_prob,
                )
                self.train_datasets.append(train_data)
                self.val_datasets.append(val_data)

        
        train_dataset_cat = torch.utils.data.dataset.ConcatDataset(self.train_datasets)
        sampler = MultiDatasetSampler(train_dataset_cat, batch_size=self.args.batch_size, shuffle=True)
        self.train_data_loader = torch.utils.data.DataLoader(train_dataset_cat, batch_size=self.args.batch_size, sampler=sampler, 
            num_workers=self.args.num_workers, pin_memory=True)
        
        val_dataset_cat = torch.utils.data.dataset.ConcatDataset(self.val_datasets)
        sampler = MultiDatasetSampler(val_dataset_cat, batch_size=self.args.val_batch_size, shuffle=False)
        self.val_data_loader = torch.utils.data.DataLoader(val_dataset_cat, batch_size=self.args.val_batch_size, sampler=sampler, 
            num_workers=self.args.num_workers, pin_memory=True)
        
        self.num_batches = len(self.train_data_loader)

    def prepare_multi_gpu(self):
        self.voxel2clip, self.optimizer, self.lr_scheduler, self.train_data_loader, self.val_data_loader = self.accelerator.prepare(
        self.voxel2clip, self.optimizer, self.lr_scheduler, self.train_data_loader, self.val_data_loader)

    def train_epoch(self, epoch):
        # train loop
        for train_i, datas in enumerate(self.train_data_loader):
            self.train_i = train_i
            print(">>> Epoch{} | Iter{} |".format(epoch, train_i), flush=True)
            self.train_step(datas)

    def eval_epoch(self, epoch):
        print("Evaluating...")
        self.voxel2clip.eval()
        for val_i, datas in enumerate(self.val_data_loader): 
            self.val_i = val_i
            print(">>> Epoch{} | Eval{} |".format(epoch, val_i), flush=True)
            self.eval_step(datas)
    

    def train_step(self, x):
        loss = 0.
        self.optimizer.zero_grad()

        clip_image =  x['image']
        clip_text = x['text']
        subj_id = x['subj']

        # Get fMRI mask if it exists, otherwise default to all ones
        fmri_mask = x.get('fmri_mask', torch.ones(clip_image.size(0), device=clip_image.device))

        if 'fmri' in x.keys():
            self.train_fmri_i += 1
            subj_index = torch.searchsorted(self.sorted_fmri_subj, subj_id)
        if 'eeg' in x.keys():
            self.train_eeg_i += 1
            subj_index = torch.searchsorted(self.sorted_eeg_subj, subj_id)+len(self.args.fmri_subj_list)
        # print('id:', subj_index[0])
        s_drop_rate = 0.1
        if torch.rand([]).item() < 1 - s_drop_rate:
            x['subj_index'] = subj_index

        clip_image_norm = nn.functional.normalize(clip_image.flatten(1), dim=-1)
        clip_text_norm = nn.functional.normalize(clip_text.flatten(1), dim=-1)

        results = self.voxel2clip.forward(x)        

        for name, recon in results['output'].items():
            if '2image' in name:
                clip_image_pred_norm = nn.functional.normalize(recon.flatten(1), dim=-1)

                # Apply mask for fMRI related losses
                if 'fmri2' in name:
                    valid_samples = fmri_mask.bool()
                    if valid_samples.sum() > 0:  # If there are any valid samples
                        loss_mse_image = nn.MSELoss()(
                            clip_image_pred_norm[valid_samples], 
                            clip_image_norm[valid_samples]
                        )
                        loss += self.args.mse_mult * loss_mse_image
                        self.loss_mse_image_sum += loss_mse_image.item()

                        if name == 'fmri2image':
                            self.sims_fmri_image += nn.functional.cosine_similarity(
                                clip_image_norm[valid_samples],
                                clip_image_pred_norm[valid_samples]
                            ).mean().item() if valid_samples.sum() > 0 else 0
                            
                            if valid_samples.sum() > 0:
                                labels = torch.arange(valid_samples.sum()).to(self.device)
                                self.fwd_percent_correct_fmri += utils.topk(
                                    utils.batchwise_cosine_similarity(
                                        clip_image_pred_norm[valid_samples], 
                                        clip_image_norm[valid_samples]
                                    ), 
                                    labels, k=1
                                )
                                self.bwd_percent_correct_fmri += utils.topk(
                                    utils.batchwise_cosine_similarity(
                                        clip_image_norm[valid_samples], 
                                        clip_image_pred_norm[valid_samples]
                                    ), 
                                    labels, k=1
                                )                            
                            # self.sims_fmri_image += nn.functional.cosine_similarity(clip_image_norm,clip_image_pred_norm).mean().item()
                            # labels = torch.arange(len(clip_image_norm)).to(self.device) 
                            # self.fwd_percent_correct_fmri += utils.topk(utils.batchwise_cosine_similarity(clip_image_pred_norm, clip_image_norm), labels, k=1)
                            # self.bwd_percent_correct_fmri += utils.topk(utils.batchwise_cosine_similarity(clip_image_norm, clip_image_pred_norm), labels, k=1)
                
                else: 
                    loss_mse_image = nn.MSELoss()(clip_image_pred_norm, clip_image_norm)
                    loss += self.args.mse_mult * loss_mse_image
                    self.loss_mse_image_sum += loss_mse_image.item()

                    if name == 'eeg2image':
                        self.sims_eeg_image += nn.functional.cosine_similarity(clip_image_norm,clip_image_pred_norm).mean().item()
                        labels = torch.arange(len(clip_image_norm)).to(self.device) 
                        self.fwd_percent_correct_eeg += utils.topk(utils.batchwise_cosine_similarity(clip_image_pred_norm, clip_image_norm), labels, k=1)
                        self.bwd_percent_correct_eeg += utils.topk(utils.batchwise_cosine_similarity(clip_image_norm, clip_image_pred_norm), labels, k=1)

                    
            if '2text' in name:
                clip_text_pred_norm = nn.functional.normalize(recon.flatten(1), dim=-1)

                if 'fmri2' in name:   
                    valid_samples = fmri_mask.bool()
                    if valid_samples.sum() > 0:
                        loss_mse_text = nn.MSELoss()(
                            clip_text_pred_norm[valid_samples], 
                            clip_text_norm[valid_samples]
                        )
                        loss += self.args.mse_mult * loss_mse_text
                        self.loss_mse_text_sum += loss_mse_text.item()

                        if name == 'fmri2text':
                            self.sims_fmri_text += nn.functional.cosine_similarity(
                                clip_text_norm[valid_samples],
                                clip_text_pred_norm[valid_samples]
                            ).mean().item() if valid_samples.sum() > 0 else 0
                else:
                    # For non-fMRI losses, continue as normal
                    loss_mse_text = nn.MSELoss()(clip_text_pred_norm, clip_text_norm)
                    loss += self.args.mse_mult * loss_mse_text
                    self.loss_mse_text_sum += loss_mse_text.item()

                    if name == 'eeg2text':
                        self.sims_eeg_text += nn.functional.cosine_similarity(
                            clip_text_norm, clip_text_pred_norm
                        ).mean().item()

            if '2fmri' in name:
                valid_samples = fmri_mask.bool()
                if valid_samples.sum() > 0:
                    loss_rec_fmri = nn.MSELoss()(
                        x['fmri'][valid_samples], 
                        recon[valid_samples]
                    )
                    loss += self.args.rec_fmri_mult * loss_rec_fmri            
                    self.loss_rec_fmri_sum += loss_rec_fmri.item()                
                # loss_rec_fmri = nn.MSELoss()(x['fmri'], recon)
                # loss += self.args.rec_fmri_mult * loss_rec_fmri            
                # self.loss_rec_fmri_sum += loss_rec_fmri.item()
            if '2eeg' in name:
                loss_rec_eeg = nn.MSELoss()(x['eeg'], recon)
                loss += self.args.rec_eeg_mult * loss_rec_eeg            
                self.loss_rec_eeg_sum += loss_rec_eeg.item()
                

        loss += results['loss_union']
        self.union_loss += results['loss_union'].item()

        if 'subj_index' in x.keys():
            loss += results['subj_loss']

        #---------------adv----------------
        def dis_func(c, subj_index):
            for _ in range(3):
                loss_disc = self.voxel2clip.discriminator(c.detach(), subj_index)
                self.optimizer_dis.zero_grad()
                loss_disc.backward()
                #print(loss_disc)
                self.optimizer_dis.step()
            loss_adv = self.voxel2clip.discriminator(c, subj_index)
            print('adv loss:', loss_adv)
            return loss_adv
        
        if 'fmri' in x.keys():
            valid_samples = fmri_mask.bool()
            if valid_samples.sum() > 0:
                c = results['emb_dict']['fmri']['c']
                # Only use valid fMRI samples for the discriminator
                loss = loss - 0.01 * dis_func(c[valid_samples], subj_index[valid_samples])            
            # c = results['emb_dict']['fmri']['c']
            # loss = loss - 0.01 * dis_func(c, subj_index)
        elif 'eeg' in x.keys():
            c = results['emb_dict']['eeg']['c']
            loss = loss - 0.01 * dis_func(c, subj_index)
        
        utils.check_loss(loss)
        self.accelerator.backward(loss)
        self.optimizer.step()

        self.losses.append(loss.item())
        self.lrs.append(self.optimizer.param_groups[0]['lr'])
        self.lr_scheduler.step()


    def eval_step(self, x):
        val_loss = 0.
        with torch.no_grad():
            clip_image =  x['image']
            clip_text = x['text']

            clip_image_norm = nn.functional.normalize(clip_image.flatten(1), dim=-1)
            clip_text_norm = nn.functional.normalize(clip_text.flatten(1), dim=-1)

            results = self.voxel2clip.forward(x)
            subj_id = x['subj']
            if 'fmri' in x.keys():
                self.val_fmri_i += 1
                subj_index = torch.searchsorted(self.sorted_fmri_subj, subj_id)
            if 'eeg' in x.keys():
                self.val_eeg_i += 1
                subj_index = torch.searchsorted(self.sorted_eeg_subj, subj_id)+len(self.args.fmri_subj_list)
            x['subj_index'] = subj_index            

            for name, recon in results['output'].items():
                if '2image' in name:
                    clip_image_pred_norm = nn.functional.normalize(recon.flatten(1), dim=-1)
                    loss_mse_image = nn.MSELoss()(clip_image_pred_norm, clip_image_norm)
                    val_loss += self.args.mse_mult * loss_mse_image
                    self.val_loss_mse_image_sum += loss_mse_image.item()

                    if name == 'fmri2image':
                        self.val_sims_fmri_image += nn.functional.cosine_similarity(clip_image_norm,clip_image_pred_norm).mean().item()
                        labels = torch.arange(len(clip_image_norm)).to(self.device) 
                        self.val_fwd_percent_correct_fmri += utils.topk(utils.batchwise_cosine_similarity(clip_image_pred_norm, clip_image_norm), labels, k=1)
                        self.val_bwd_percent_correct_fmri += utils.topk(utils.batchwise_cosine_similarity(clip_image_norm, clip_image_pred_norm), labels, k=1)
                    
                    elif name == 'eeg2image':
                        self.val_sims_eeg_image += nn.functional.cosine_similarity(clip_image_norm,clip_image_pred_norm).mean().item()
                        labels = torch.arange(len(clip_image_norm)).to(self.device) 
                        self.val_fwd_percent_correct_eeg += utils.topk(utils.batchwise_cosine_similarity(clip_image_pred_norm, clip_image_norm), labels, k=1)
                        self.val_bwd_percent_correct_eeg += utils.topk(utils.batchwise_cosine_similarity(clip_image_norm, clip_image_pred_norm), labels, k=1)

                if '2text' in name:
                    clip_text_pred_norm = nn.functional.normalize(recon.flatten(1), dim=-1)
                    loss_mse_text = nn.MSELoss()(clip_text_pred_norm, clip_text_norm)
                    val_loss += self.args.mse_mult * loss_mse_text
                    self.val_loss_mse_text_sum += loss_mse_text.item()

                    if name == 'fmri2text':
                        self.val_sims_fmri_text += nn.functional.cosine_similarity(clip_text_norm,clip_text_pred_norm).mean().item()
                    elif name == 'eeg2text':
                        self.val_sims_eeg_text += nn.functional.cosine_similarity(clip_text_norm,clip_text_pred_norm).mean().item()

                if '2fmri' in name:
                    loss_rec_fmri = nn.MSELoss()(x['fmri'], recon)
                    val_loss += self.args.rec_fmri_mult * loss_rec_fmri            
                    self.val_loss_rec_fmri_sum += loss_rec_fmri.item()
                if '2eeg' in name:
                    loss_rec_eeg = nn.MSELoss()(x['eeg'], recon)
                    val_loss += self.args.rec_eeg_mult * loss_rec_eeg            
                    self.val_loss_rec_eeg_sum += loss_rec_eeg.item()

            self.val_union_loss += results['loss_union'].item()
            utils.check_loss(val_loss)
            self.val_losses.append(val_loss.item())