import os
import torch
import numpy as np
from PIL import Image
from torch import nn
import torch.nn.functional as F
from torch.utils.data import Dataset, DataLoader
import sys
import torchvision.transforms as transforms
import utils
from options import args

sys.path.append('/root/workspace/multi_vae/')
from models.nn.encoder import create_brain_encoder, create_clip_encoder, create_union_encoder, create_sub_encoder
from models.nn.decoder import create_brain_decoder, create_clip_decoder, create_union_decoder, create_sub_decoder, create_modality_decoder
from models import __all__ as available_models
from classification.class_nsd import ClassNsd

from importlib import import_module

def config_multi_gpu():
    # Multi-GPU config
    deepspeed_plugin = DeepSpeedPlugin(zero_stage=2, gradient_clipping=1.0)
    accelerator = Accelerator(split_batches=False, mixed_precision='no', deepspeed_plugin=deepspeed_plugin)  
    accelerator.print("PID of this process =",os.getpid())
    device = accelerator.device
    accelerator.print("device:",device)
    num_devices = torch.cuda.device_count()
    if num_devices==0: num_devices = 1
    accelerator.print(accelerator.state)
    local_rank = accelerator.state.local_process_index
    world_size = accelerator.state.num_processes
    distributed = not accelerator.state.distributed_type == 'NO'
    accelerator.print("distributed =",distributed, "num_devices =", num_devices, "local rank =", local_rank, "world size =", world_size)
    return accelerator, device, local_rank
def prepare_voxel2clip(args, device):

    # define encoders and decoders
    encoders = {}
    decoders = {}

    if 'fmri' in args.use_modalities:
        encoders['fmri'] = create_brain_encoder(in_dim_brain=args.pool_num, h=args.h_size)
        decoders['fmri'] = create_brain_decoder(in_dim_brain=args.pool_num, h=args.h_size)
    if 'eeg' in args.use_modalities:
        encoders['eeg'] = create_brain_encoder(in_dim_brain=args.eeg_size, h=args.h_size)
        decoders['eeg'] = create_brain_decoder(in_dim_brain=args.eeg_size, h=args.h_size)
    if 'image' in args.use_modalities:
        reduce_channel = 20
        channel = 257
        if args.use_mean_feature:
            reduce_channel = 1
            channel = 1
        encoders['image'] = create_clip_encoder(h=args.h_size, reduce_channel=reduce_channel, channel=channel, clip_size=1024)
        decoders['image'] = create_clip_decoder(h=args.h_size, reduce_channel=reduce_channel, channel=channel, clip_size=1024)
    if 'text' in args.use_modalities:
        reduce_channel = 10
        channel = 77
        if args.use_mean_feature:
            reduce_channel = 1
            channel = 1
        encoders['text'] = create_clip_encoder(h=args.h_size, reduce_channel=reduce_channel, channel=channel, clip_size=1024)
        decoders['text'] = create_clip_decoder(h=args.h_size, reduce_channel=reduce_channel, channel=channel, clip_size=1024)
    encoders['union'] = create_union_encoder(h=args.h_size)
    decoders['union'] = create_union_decoder(h=args.h_size)

    
    if args.model_name not in available_models:
        raise ValueError(f"Model '{args.model_name}' is not available. Available models are: {available_models}")

    module = import_module(f'models')
    model_class = getattr(module, args.model_name)
    model_config = getattr(module, args.model_name+"Config")
    config = model_config(latent_dim=args.h_size//2, use_modalities=args.use_modalities)

    if args.model_name == 'MIDAS' or '_DIS' in args.model_name:
        config.len_subj = args.len_subj
        encoders['subj'] = create_sub_encoder(args.len_subj, config.sub_dim)
        decoders['subj'] = create_sub_decoder(args.len_subj, config.sub_dim)
    
    if args.use_modality_specific_spaces:
        config.use_modality_specific_spaces = True
        config.modalities_specific_dim = args.modalities_specific_dim
        decoders['mod'] = create_modality_decoder(len(args.use_modalities), config.modalities_specific_dim)

    voxel2clip = model_class(config, encoders, decoders).to(device)

    outdir = f'./train_logs_fmri_eeg/{args.exp_name}'
    ckpt_path = os.path.join(outdir, f'{args.ckpt_from}.pth')
    print("ckpt_path",ckpt_path)
    checkpoint = torch.load(ckpt_path, map_location='cpu')
    print("EPOCH: ",checkpoint['epoch'])
    state_dict = checkpoint['model_state_dict']

    filtered_dict = {k: v for k, v in checkpoint['model_state_dict'].items() if "discriminator" not in k and "classify" not in k}

    voxel2clip.load_state_dict(filtered_dict,strict=False)
    voxel2clip.requires_grad_(False)
    return voxel2clip

args.model_name = 'MMVAEPlus_DIS'
args.exp_name = args.model_name + '_fmri_eeg_4+4_1024_2_512_10000_20'
args.use_modalities = ["fmri", "image", "text", "eeg"]
args.pool_num = 8192
args.batch_size = 100
args.h_size = 512
args.eeg_size = 1900
args.use_mean_feature = True
args.use_modality_specific_spaces = True

args.fmri_subj_list = [1, 2, 5, 7]
args.eeg_subj_list = [5, 6, 7, 8]

accelerator, device, local_rank = config_multi_gpu()
utils.seed_everything(args.seed, cudnn_deterministic=False)

len_subj = 0
if 'fmri' in args.use_modalities:
    len_subj += len(args.fmri_subj_list)
if 'eeg' in args.use_modalities:
    len_subj += len(args.eeg_subj_list)
args.len_subj = len_subj

args.max_lr *= accelerator.num_processes

voxel2clip = prepare_voxel2clip(args, device)

# Init Trainer
trainer = ClassNsd(args, accelerator, voxel2clip, device)
# trainer.load()
trainer.train(local_rank)