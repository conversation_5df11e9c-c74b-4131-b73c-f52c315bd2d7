import torch
import math
import torch.nn as nn
from models.base import BaseModel
from models.nn.base_arch import Discriminator


#---------------------------------------------------------------------
def exp(x, eps=1e-12):
    return (x < 0) * (x.clamp(max=0)).exp() + (x >= 0) / ((-x.clamp(min=0)).exp() + eps)

def log(x, eps=1e-12):
    return (x + eps).log()

def poe(mus, logvars):
    """
    Product of Experts
    - mus: [mu_1, ..., mu_M], where mu_m is N * K
    - logvars: [logvar_1, ..., logvar_M], where logvar_m is N * K
    """
    
    # mus = [torch.full_like(mus[0], 0)] + mus
    # logvars = [torch.full_like(logvars[0], 0)] + logvars
    
    mus_stack = torch.stack(mus, dim=1)  # N * M * K
    logvars_stack = torch.stack(logvars, dim=1)
    
    T = exp(-logvars_stack)  # precision of i-th Gaussian expert at point x
    T_sum = T.sum(1)  # N * K
    pd_mu = (mus_stack * T).sum(1) / T_sum
    pd_var = 1 / T_sum
    pd_logvar = log(pd_var)
    return pd_mu, pd_logvar  # N * K

def sample_gaussian(mu, logvar):
    std = (0.5*logvar).exp()
    eps = torch.randn_like(std)
    return mu + std*eps

def calc_kld_loss(mu, logvar):
    return (-0.5 * (1 + logvar - mu.pow(2) - logvar.exp())).sum() / mu.size(0)
#---------------------------------------------------------------------


class MIDAS(BaseModel):
    def __init__(self, model_config, encoders, decoders):
        super().__init__(model_config, encoders, decoders)

        # -------------discriminator-------------------
        self.len_subj = model_config.len_subj
        self.discriminator = Discriminator(self.len_subj, model_config.latent_dim-model_config.sub_dim)

    
    def encoding(self, x, x_type):
        x = self.encoders[x_type](x)
        x = self.encoders['union'](x)
        x_mu, x_logvar = x.split([self.model_config.latent_dim, self.model_config.latent_dim], dim=1)
        if self.training:
            x = sample_gaussian(x_mu, x_logvar)
        else:
            x = x_mu
        c, b = x.split([self.model_config.latent_dim-self.model_config.sub_dim, self.model_config.sub_dim], dim=1)
        return {'c':c, 'b':b, 'x_mu':x_mu, 'x_logvar':x_logvar}

    def decoding_union(self, c, b):
        x = torch.concat([c, b], dim=1)
        x = self.decoders['union'](x)
        return x
    
    def decoding(self, x, x_type):
        x = self.decoders[x_type](x)
        return x

    def forward(self, x):
        print('--------vae version--------')
        #-------------encoding------------
        emb_dict = {}
        for key, value in x.items():
            # if key in ['image', 'text']:
            #     value = value.flatten(1)
            if key in self.model_config.use_modalities:
                emb_dict[key] = self.encoding(value, key)
        
        if 'subj_index' in x.keys():
            sub_b = self.encoders['subj'](nn.functional.one_hot(x['subj_index'], num_classes=self.len_subj).float())
        
        #-------------fusion---------------
        mu_list = []
        logvar_list = []
        for key, value in emb_dict.items():
            mu_list.append(value['x_mu'])
            logvar_list.append(value['x_logvar'])
        fusion_mu, fusion_logvar = poe(mu_list, logvar_list)
        if self.training:
            fusion = sample_gaussian(fusion_mu, fusion_logvar)
        else:
            fusion = fusion_mu
        c, b = fusion.split([self.model_config.latent_dim-self.model_config.sub_dim, self.model_config.sub_dim], dim=1)
        fusion_emb = {'c':c, 'b':b, 'x_mu':fusion_mu, 'x_logvar':fusion_logvar}
        emb_dict['fusion'] = fusion_emb     

        #---------------decoding union------------
        if 'eeg' in x.keys():
            brain_b = emb_dict['eeg']['b']
        elif 'fmri' in x.keys():
            brain_b = emb_dict['fmri']['b']
        
        if 'subj_index' in x.keys():
            brain_b_ = brain_b
            brain_b = (brain_b_ + sub_b)/2

        u_dec = {}
        for key, value in emb_dict.items():
            u_dec[key] = self.decoding_union(value['c'], brain_b)
       
        #--------------decoding------------
        output = {}
        for key, value in u_dec.items():
            for dec_type in u_dec.keys():
                if dec_type != 'fusion':
                    print(f'{key}2{dec_type}')
                    recon = self.decoding(value, dec_type)
                    output[f'{key}2{dec_type}'] = recon
        
        #------------embedding loss------------
        kl_loss = 0
        loss_union = 0
        for key, value in emb_dict.items():
            if key != 'fusion':
                kl_loss += calc_kld_loss(value['x_mu'], value['x_logvar'])
                loss_union += nn.MSELoss()(value['c'], c)
        loss_union = 1 * loss_union + 1e-5 * kl_loss

        #-----------------s----------------------
        subj_loss=0
        if 'subj_index' in x.keys():
            sub_pre = self.decoders['subj'](brain_b)
            # print('pred sub', sub_pre[:10])
            # print('gt sub', x['subj_index'][:10])
            subj_predict_loss = nn.CrossEntropyLoss()(sub_pre, x['subj_index'])
            #print('subj predict loss', subj_predict_loss)
            subj_union_loss = nn.MSELoss()(brain_b_, sub_b)
            #print('subj union loss', subj_union_loss)
            subj_loss = 0.01 * subj_predict_loss + subj_union_loss

            pred_labels = torch.argmax(sub_pre, dim=1)
            accuracy = (pred_labels == x['subj_index']).float().mean()
            print('subj_index accuracy', accuracy)

        return {'output':output, 'loss_union':loss_union, 'emb_dict':emb_dict, 'subj_loss': subj_loss, 'brain_b':brain_b}
    

    def forward_recon(self, voxel, modality):
        voxel = self.encoding(voxel, modality)
        voxel = self.decoders['union'](voxel['x_mu'])
        voxel2img = self.decoders['image'](voxel)
        voxel2txt = self.decoders['text'](voxel)
        return voxel2img, voxel2txt