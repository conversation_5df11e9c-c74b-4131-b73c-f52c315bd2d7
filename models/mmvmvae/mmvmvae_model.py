import torch
import torch.nn as nn
import math
from torch.distributions import <PERSON><PERSON>, Normal
from pythae.models.base.base_utils import ModelOutput

from models.base import BaseModel

class MMVMVAE(BaseModel):
    def __init__(self, model_config, encoders, decoders) -> None:
        super().__init__(model_config, encoders, decoders)
        
        self.model_config = model_config
        self.global_step = 0
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        self.modalities_size = {
            'fmri': 8192,
            'image': 768,
            'text': 768,
            'eeg': 1900
        }
        
        # 设置参考模态维度（使用最小的维度作为参考）
        self.ref_mod_d_size = min(self.modalities_size.values())
        
        if model_config.temp_annealing == "cosine":
            self.compute_current_temperature = self.cos_annealing
        elif model_config.temp_annealing == "linear":
            self.compute_current_temperature = self.linear_annealing
        elif model_config.temp_annealing == "exp":
            self.compute_current_temperature = self.exp_annealing
            
    def forward(self, x):
        dists_enc_out = {}
        dists_out = {}
        mods_rec = {}
        embeddings = {}
        
        mu_mean = 0
        log_var_mean = 0
        num_modalities = 0

        for cond_mod, value in x.items():
            if cond_mod in self.model_config.use_modalities:
                output = self.encoding(value, cond_mod)
                mu, log_var = output['embedding'], output['log_covariance']

                mu_mean += mu
                log_var_mean += log_var
                num_modalities += 1

                dists_enc_out[cond_mod] = [mu, log_var]
                z = self.reparametrize(mu, log_var)
                embeddings[cond_mod] = z
                dists_out[cond_mod] = [mu, log_var]

                mods_rec[cond_mod] = {}
                for recon_mod in x.keys():
                    if recon_mod in self.model_config.use_modalities:
                        recon = self.decoding(z, recon_mod)
                        mods_rec[cond_mod][recon_mod] = recon                               
                
        if self.training:
            loss_output = self.compute_loss(x, mods_rec, dists_out)
        else:
            loss_output = ModelOutput()
            
        ##------ add fusion -------
        mu_mean /= num_modalities
        log_var_mean /= num_modalities
        z_mean = self.reparametrize(mu_mean, log_var_mean)

        mods_rec['fusion'] = {}
        for recon_mod in x.keys():
            if recon_mod in self.model_config.use_modalities:
                recon = self.decoding(z_mean, recon_mod)
                mods_rec['fusion'][recon_mod] = recon
        embeddings['fusion'] = z_mean
        ##-------------------------
          
        loss_output.update({
            'dists_out': dists_out,
            'dists_enc_out': dists_enc_out,
            'recon': mods_rec,
            "zss": embeddings,
            "sample_k": False
        })
        
        return loss_output
        
    def compute_loss(self, x, mods_rec, dists_out):
        rec_loss, rec_loss_mods, rec_loss_mods_weighted = self.compute_rec_loss(x, mods_rec)
        
        if self.model_config.alpha_annealing:
            alpha_weight = self.compute_current_temperature(
                self.model_config.init_alpha_value,
                self.model_config.final_alpha_value, 
                self.model_config.alpha_annealing_steps
            )
        else:
            alpha_weight = self.model_config.final_alpha_value
            
        klds = []
        for mod in mods_rec.keys():
            dist_m = dists_out[mod]
            for mod_tilde in mods_rec.keys():
                dist_m_tilde = dists_out[mod_tilde]
                kld_m_m_tilde = self.kl_div_z_two_dists(dist_m, dist_m_tilde)
                klds.append(kld_m_m_tilde.unsqueeze(1) * (1.0 - alpha_weight))
                
            # 添加标准正态先验
            kld_m = self.kl_div_z(dist_m)
            klds.append(kld_m.unsqueeze(1) * alpha_weight * 3)
            
        klds_sum = torch.cat(klds, dim=1).sum(dim=1) / 3
        
        # 计算总损失
        loss = (rec_loss + self.model_config.beta * klds_sum).mean()
        
        metrics = {
            'loss': loss,
            'rec_loss': rec_loss.mean(),
            'kl_loss': klds_sum.mean(),
            'alpha': alpha_weight
        }
        
        for mod in rec_loss_mods:
            metrics[f'rec_loss_{mod}'] = rec_loss_mods[mod]
            metrics[f'rec_loss_weighted_{mod}'] = rec_loss_mods_weighted[mod]
            
        return ModelOutput(loss=loss, metrics=metrics)

    def compute_rec_loss(self, data, data_rec):
        """计算重构损失，包括同模态重构和跨模态重构
        Args:
            data: 原始输入数据字典
            data_rec: 重构数据字典，结构为 data_rec[cond_mod][recon_mod] = recon
        Returns:
            rec_loss_avg: 平均重构损失
            rec_loss_mods: 每个模态的重构损失
            rec_loss_mods_weighted: 每个模态的加权重构损失
        """
        rec_loss_all = []
        rec_loss_mods = {}
        rec_loss_mods_weighted = {}
        
        # 遍历条件模态（用于编码的模态）
        for cond_mod in data.keys():
            if cond_mod not in self.model_config.use_modalities:
                continue
                
            # 遍历目标重构模态
            for recon_mod in data.keys():
                if recon_mod not in self.model_config.use_modalities:
                    continue
                    
                # 获取真实数据和重构数据
                gt_data = data[recon_mod]
                rec_data = data_rec[cond_mod][recon_mod]
                
                # 计算重构权重
                rec_weight = float(self.ref_mod_d_size / self.modalities_size[recon_mod])
                
                # 计算log概率
                log_prob = self._compute_mod_log_prob(recon_mod, gt_data, rec_data)
                
                loss_key = f"{cond_mod}2{recon_mod}"
                rec_loss_mods[loss_key] = log_prob.mean(dim=0)
                rec_loss_mods_weighted[loss_key] = (rec_weight * log_prob).mean(dim=0)
                rec_loss_all.append(rec_weight * log_prob.unsqueeze(1))
                # rec_loss_mods_weighted[loss_key] = log_prob.mean(dim=0)
                # rec_loss_all.append(log_prob.unsqueeze(1))

        rec_loss_avg = -torch.cat(rec_loss_all, dim=1).sum(dim=1)
        return rec_loss_avg, rec_loss_mods, rec_loss_mods_weighted

    def _compute_mod_log_prob(self, mod_name, gt_data, rec_data):
        """计算单个模态的对数概率
        Args:
            mod_name: 模态名称
            gt_data: 真实数据
            rec_data: 重构数据，维度为：
                - fmri: [batch, 8192]
                - eeg: [batch, 1900]
                - image: [batch, 768]
                - text: [batch, 768]
        Returns:
            log_prob: 对数概率 [batch]
        """
        if mod_name in ["text", "image"]:
            # 文本和图像模态，需要reshape成 [batch * seq_len, hidden_dim]
            # batch_size, seq_len, hidden_dim = gt_data.shape
            # gt_flat = gt_data.reshape(-1, hidden_dim)
            # rec_flat = rec_data.reshape(-1, hidden_dim)
            
            dist = torch.distributions.laplace.Laplace(
                rec_data, 
                torch.tensor(0.75).to(self.device)
            )
            log_prob = dist.log_prob(gt_data)
            return log_prob.sum(dim=1)
            
        elif mod_name in ["fmri", "eeg"]:
            dist = torch.distributions.laplace.Laplace(
                rec_data, 
                torch.tensor(0.75).to(self.device)
            )
            return dist.log_prob(gt_data).sum(dim=1)
            
        else:
            raise ValueError(f"Unknown modality: {mod_name}")
    
    def reparametrize(self, mu, logvar):
        std = logvar.mul(0.5).exp_()
        eps = std.data.new(std.size()).normal_()
        return eps.mul(std).add_(mu)
        
    def kl_div_z(self, dist):
        """计算与标准正态先验的KL散度"""
        mu, logvar = dist
        return -0.5 * torch.sum(1 - logvar.exp() - mu.pow(2) + logvar, dim=-1)
        
    def kl_div_z_two_dists(self, dist1, dist2):
        """计算两个分布之间的KL散度"""
        mu1, logvar1 = dist1
        mu2, logvar2 = dist2
        return -0.5 * (torch.sum(
            1 - logvar1.exp() / logvar2.exp() 
            - (mu1 - mu2).pow(2) / logvar2.exp()
            + logvar1 - logvar2,
            dim=-1
        ))

    # 温度退火相关函数
    def cos_annealing(self, init_temp, final_temp, num_steps_annealing):
        curr_temp = final_temp + 0.5 * (init_temp - final_temp) * (
            1 + torch.cos(torch.tensor((self.global_step / num_steps_annealing) * math.pi))
        )
        return curr_temp if self.global_step < num_steps_annealing else final_temp
        
    def linear_annealing(self, init_temp, final_temp, num_steps):
        if self.global_step < num_steps:
            return (1 - self.global_step/num_steps) * init_temp + (self.global_step/num_steps) * final_temp
        return final_temp
        
    def exp_annealing(self, init_temp, final_temp, num_steps):
        rate = (math.log(final_temp + 1e-10) - math.log(init_temp + 1e-10)) / float(num_steps)
        curr_temp = max(init_temp * math.exp(rate * self.global_step), final_temp)
        return curr_temp