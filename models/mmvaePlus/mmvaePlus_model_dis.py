import torch
import math
import torch.nn as nn

import torch.nn.functional as F
from torch.distributions import Laplace, Normal
from pythae.models.base.base_utils import ModelOutput

from models.base import BaseModel
from models.nn.base_arch import Discriminator

class MMVAEPlus_DIS(BaseModel):
    def __init__(self, model_config, encoders, decoders) -> None:
        super().__init__(model_config, encoders, decoders)
        print('current model is mmvaeplus')
        
        if model_config.prior_and_posterior_dist == "laplace_with_softmax":
            self.post_dist = Laplace
            self.prior_dist = Laplace
        elif model_config.prior_and_posterior_dist == "normal":
            self.post_dist = Normal
            self.prior_dist = Normal
        elif model_config.prior_and_posterior_dist == "normal_with_softplus":
            self.post_dist = Normal
            self.prior_dist = Normal
        else:
            raise AttributeError(
                " The posterior_dist parameter must be "
                " either 'laplace_with_softmax','normal' or 'normal_with_softplus'. "
                f" {model_config.prior_and_posterior_dist} was provided."
            )
        
        # Set the priors for shared and private spaces.
        self.mean_priors = torch.nn.ParameterDict()
        self.logvars_priors = torch.nn.ParameterDict()
        self.beta = model_config.beta
        self.modalities_specific_dim = model_config.modalities_specific_dim
        self.reconstruction_option = model_config.reconstruction_option
        self.multiple_latent_spaces = True
        self.style_dims = {m: self.modalities_specific_dim for m in self.encoders}

        # Add the private and shared latents priors.

        # modality specific priors (referred to as r distributions in paper)
        for mod in list(self.encoders.keys()):
            self.mean_priors[mod] = torch.nn.Parameter(
                torch.zeros(1, model_config.modalities_specific_dim),
                requires_grad=False,
            )
            self.logvars_priors[mod] = torch.nn.Parameter(
                torch.zeros(1, model_config.modalities_specific_dim),
                requires_grad=model_config.learn_modality_prior,
            )

        # general prior (for the entire latent code) referred to as p in the paper
        self.mean_priors["shared"] = torch.nn.Parameter(
            torch.zeros(
                1, model_config.latent_dim
            ),
            requires_grad=False,
        )
        self.logvars_priors["shared"] = torch.nn.Parameter(
            torch.zeros(
                1, model_config.latent_dim
            ),
            requires_grad=model_config.learn_shared_prior,
        )
   
        self.objective = model_config.loss

        # -------------discriminator-------------------
        self.len_subj = model_config.len_subj

        input_size = model_config.latent_dim-model_config.sub_dim
        # if self.multiple_latent_spaces:
        #     input_size = input_size - model_config.modalities_specific_dim
        self.discriminator = Discriminator(self.len_subj, input_size)

        if self.multiple_latent_spaces:
            # self.discriminator_modality = nn.ModuleDict()
            # self.discriminator_modality['fmri'] = Discriminator(self.len_subj, model_config.modalities_specific_dim)
            # self.discriminator_modality['eeg'] = Discriminator(self.len_subj, model_config.modalities_specific_dim)
            #input_size = input_size - model_config.modalities_specific_dim
            input_size = model_config.latent_dim-model_config.modalities_specific_dim
            self.discriminator_modality = Discriminator(len(self.used_modalities), input_size)
    def log_var_to_std(self, log_var):
        """
        For latent distributions parameters, transform the log covariance to the
        standard deviation of the distribution either applying softmax, softplus
        or simply torch.exp(0.5 * ...) depending on the model configuration.
        """

        if self.model_config.prior_and_posterior_dist == "laplace_with_softmax":
            return F.softmax(log_var, dim=-1) * log_var.size(-1) + 1e-6
        elif self.model_config.prior_and_posterior_dist == "normal_with_softplus":
            return F.softplus(log_var) + 1e-6
        else:
            return torch.exp(0.5 * log_var)
    
    def compute_posteriors_and_embeddings(self, inputs, detach):

        # First compute all the encodings for all modalities
        embeddings = {}
        posteriors = {m: {} for m in inputs if m in self.used_modalities}
        reconstructions = {}
        embed_mu = {}
        modality_mu = {}


        temp = {}
        for cond_mod in inputs:
            if cond_mod in self.used_modalities:
                #output = self.encoders[cond_mod](inputs[cond_mod])
                output = self.encoding(inputs[cond_mod], cond_mod, style_dim=self.modalities_specific_dim, subj_dim=self.model_config.sub_dim)
                temp[cond_mod] = output


        #--------------------add  subj----------------------
        if 'subj_index' in inputs.keys():
            sub_b = self.encoders['subj'](nn.functional.one_hot(inputs['subj_index'], num_classes=self.len_subj).float())
        if 'eeg' in inputs.keys():
            brain_b = temp['eeg']['subj_embedding']
        elif 'fmri' in inputs.keys():
            brain_b = temp['fmri']['subj_embedding']
        if 'subj_index' in inputs.keys():
            brain_b_ = brain_b
            brain_b = (brain_b_ + sub_b)/2
        brain_b_k = brain_b.unsqueeze(0)
        sub_b_k = sub_b.unsqueeze(0)
        #---------------------------------------------------


        for cond_mod in inputs:
            if cond_mod in self.used_modalities:
                output = temp[cond_mod]
                mu, log_var = output['embedding'], output['log_covariance']
                mu_style = output['style_embedding']
                log_var_style = output['style_log_covariance']

                sigma = self.log_var_to_std(log_var)
                sigma_style = self.log_var_to_std(log_var_style)

                # Shared latent variable
                qu_x = self.post_dist(mu, sigma)
                u_x = qu_x.rsample([self.model_config.K])

                # Private latent variable
                qw_x = self.post_dist(mu_style, sigma_style)
                w_x = qw_x.rsample([self.model_config.K])

                # The DREG loss uses detached parameters in the loss computation afterwards.
                if detach:
                    qu_x = self.post_dist(mu.clone().detach(), sigma.clone().detach())
                    qw_x = self.post_dist(
                        mu_style.clone().detach(), sigma_style.clone().detach()
                    )

                # Then compute all the cross-modal reconstructions
                reconstructions[cond_mod] = {}

                if cond_mod in ['fmri', 'eeg']:
                    brain = brain_b_k
                else:
                    brain = sub_b_k

                for recon_mod in inputs:
                    if recon_mod in self.used_modalities:
                        if recon_mod not in ['fmri', 'eeg']:
                            brain_target = brain * 0
                        else:
                            brain_target = brain

                        # Self-reconstruction
                        if recon_mod == cond_mod:
                            z_x = torch.cat([u_x, w_x, brain_target], dim=-1)

                        # Cross modal reconstruction
                        else:
                            # only keep the shared latent and generate private from prior
                            mu_prior_mod = torch.cat(
                                [self.mean_priors[recon_mod]] * len(mu), axis=0
                            )
                            sigma_prior_mod = torch.cat(
                                [self.log_var_to_std(self.logvars_priors[recon_mod])] * len(mu),
                                axis=0,
                            )

                            w = self.prior_dist(
                                mu_prior_mod,
                                sigma_prior_mod,
                            ).rsample([self.model_config.K])

                            z_x = torch.cat([u_x, w, brain_target], dim=-1)
                        # Decode

                        z = z_x.reshape(-1, z_x.shape[-1])
                        #recon = self.decoders[recon_mod](z)["reconstruction"]
                        recon = self.decoding(z, recon_mod)
                        recon = recon.reshape((*z_x.shape[:-1], *recon.shape[1:]))
                        reconstructions[cond_mod][recon_mod] = recon

                posteriors[cond_mod] = {"u": qu_x, "w": qw_x}
                embeddings[cond_mod] = {"u": u_x, "w": w_x, "s":brain_b_k}
                embed_mu[cond_mod] = mu.unsqueeze(0) #share
                modality_mu[cond_mod] = mu_style


        subj_loss=0
        if 'subj_index' in inputs.keys():
            sub_pre = self.decoders['subj'](brain_b)
            print('pred sub', sub_pre[:10])
            print('gt sub', inputs['subj_index'][:10])
            subj_predict_loss = nn.CrossEntropyLoss()(sub_pre, inputs['subj_index'])
            #print('subj predict loss', subj_predict_loss)
            subj_union_loss = nn.MSELoss()(brain_b_, sub_b)
            #print('subj union loss', subj_union_loss)
            subj_loss = 10000*subj_predict_loss + 1000000*subj_union_loss

            pred_labels = torch.argmax(sub_pre, dim=1)
            accuracy = (pred_labels == inputs['subj_index']).float().mean()
            print('subj_index accuracy', accuracy)

        mod_predict_loss = 0
        if self.multiple_latent_spaces:
            mod_embeddings = []
            mod_labels = []
            for cond_mod in self.used_modalities:
                    if cond_mod in embeddings.keys():
                        mod_embeddings.append(embeddings[cond_mod]["w"][0])
                        num = embeddings[cond_mod]["w"][0].shape[0]
                        mod_labels.extend([self.used_modalities.index(cond_mod)]*num)
            print('gt mod', mod_labels[-10:])
            mod_embeddings = torch.cat(mod_embeddings)
            mod_labels = torch.tensor(mod_labels).to(mod_embeddings.device)

            mod_pre = self.decoders['mod'](mod_embeddings)
            mod_predict_loss = 10000*nn.CrossEntropyLoss()(mod_pre, mod_labels)

            # print(mod_pre)
            # print(mod_labels)
         
            pred_labels = torch.argmax(mod_pre, dim=1)
            accuracy = (pred_labels == mod_labels).float().mean()
            print('mod_index accuracy', accuracy)
        return embeddings, posteriors, reconstructions, embed_mu, subj_loss, sub_b, modality_mu, mod_predict_loss, mod_labels
    
    def get_result(self, embeddings, reconstructions, embed_mu):
    
        share_mean = 0
        for key, value in embeddings.items():
            share_mean += value["u"]
        share_mean /= self.n_modalities

        share_mu_mean = 0
        for key, value in embed_mu.items():
            share_mu_mean += value
        share_mu_mean /= self.n_modalities
        embed_mu['fusion'] = share_mu_mean

        first_key = next(iter(embeddings))
        brain_b = embeddings[first_key]['s']

        reconstructions['fusion'] = {}
        for recon_mod in self.used_modalities:
            # only keep the shared latent and generate private from prior
            mu_prior_mod = torch.cat(
                [self.mean_priors[recon_mod]] * len(share_mean[0]), axis=0
            )
            sigma_prior_mod = torch.cat(
                [self.log_var_to_std(self.logvars_priors[recon_mod])] * len(share_mean[0]),
                axis=0,
            )

            w = self.prior_dist(
                mu_prior_mod,
                sigma_prior_mod,
            ).rsample([self.model_config.K])

            z_x = torch.cat([share_mean, w, brain_b], dim=-1)
            z = z_x.reshape(-1, z_x.shape[-1])
            #recon = self.decoders[recon_mod](z)["reconstruction"]
            recon = self.decoding(z, recon_mod)
            recon = recon.reshape((*z_x.shape[:-1], *recon.shape[1:]))
            reconstructions['fusion'][recon_mod] = recon

        return embed_mu, reconstructions


    def forward(self, x):
        if self.objective == "dreg_looser":
            # The DreG estimation uses detached posteriors
            embeddings, posteriors, reconstructions, embed_mu, subj_loss, brain_b, modality_mu, mod_predict_loss, mod_labels = (
                self.compute_posteriors_and_embeddings(x, detach=True)
            )
            if self.training:
                loss_output =  self.dreg_looser(posteriors, embeddings, reconstructions, x)
            else:
                loss_output = ModelOutput()

        elif self.objective == "iwae_looser":
            embeddings, posteriors, reconstructions, embed_mu, subj_loss, brain_b, modality_mu, mod_predict_loss, mod_labels = (
                self.compute_posteriors_and_embeddings(x, detach=False)
            )
            if self.training:
                loss_output =  self.iwae_looser(posteriors, embeddings, reconstructions, x)
            else:
                loss_output = ModelOutput()
        else:
            raise NotImplemented()
        
        embeddings_share, reconstructions = self.get_result(embeddings, reconstructions, embed_mu)
        loss_output["zss"] = embeddings_share # share mu
        loss_output['subj_loss'] = subj_loss
        loss_output["recon"] = reconstructions
        loss_output["sample_k"] = True
        loss_output['brain_b'] = brain_b
        loss_output['modality_mu'] = modality_mu
        loss_output['mod_predict_loss'] = mod_predict_loss 
        loss_output['mod_labels'] = mod_labels
        loss_output['embeddings'] = embeddings
        return loss_output

    def forward_encoder(self, voxel, modality):
        embedding = self.encoding(voxel, modality)
        return embedding

    def forward_decoder(self, voxel, modality):
        recon = self.decoding(voxel, modality)
        return recon

    def forward_recon(self, voxel, modality):
        output = self.encoding(voxel, modality, self.modalities_specific_dim)
        
        mu, log_var = output['embedding'], output['log_covariance']
        sigma = self.log_var_to_std(log_var)

        qu_x = self.post_dist(mu, sigma)
        u_x = qu_x.rsample([1])


        def get_recon(u_x, recon_mod):
            mu_prior_mod = torch.cat(
                [self.mean_priors[recon_mod]] * len(mu), axis=0
            )
            sigma_prior_mod = torch.cat(
                [self.log_var_to_std(self.logvars_priors[recon_mod])] * len(mu),
                axis=0,
            )

            w = self.prior_dist(
                mu_prior_mod,
                sigma_prior_mod,
            ).rsample([self.model_config.K])

            z_x = torch.cat([u_x, w], dim=-1)
            # Decode
            z = z_x.reshape(-1, z_x.shape[-1])
            #recon = self.decoders[recon_mod](z)["reconstruction"]
            recon = self.decoding(z, recon_mod)
            recon = recon.reshape((*z_x.shape[:-1], *recon.shape[1:]))
            return recon
        
        voxel2img = get_recon(u_x, 'image')[0]
        voxel2txt = get_recon(u_x, 'text')[0]
        return voxel2img, voxel2txt
    

    @property
    def pz_params(self):
        """From the prior mean and log_covariance, return the mean and standard
        deviation, either applying softmax or not depending on the choice of prior
        distribution.

        Returns:
            tuple: mean, std
        """
        mean = self.mean_priors["shared"]
        log_var = self.logvars_priors["shared"]
        std = self.log_var_to_std(log_var)
        return mean, std

    def compute_k_lws(self, posteriors, embeddings, reconstructions, inputs):
        """Compute the individual likelihoods without any aggregation on k_iwae
        or the batch."""

        if hasattr(inputs, "masks"):
            # Compute the number of available modalities per sample
            n_mods_sample = torch.sum(
                torch.stack(tuple(inputs.masks.values())).int(), dim=0
            )
        else:
            n_mods_sample = torch.tensor([self.n_modalities])

        lws = {}

        # print(embeddings)
        # print(posteriors)
        # input()

        for mod in embeddings:

            u = embeddings[mod]["u"]  # (K, n_batch, latent_dim)
            w = embeddings[mod]["w"]  # (K, n_batch, latent_dim)
            n_mods_sample = n_mods_sample.to(u.device)

            ### Compute log p(z)
            z = torch.cat([u, w, embeddings[mod]["s"]], dim=-1)
            lpz = self.prior_dist(*self.pz_params).log_prob(z).sum(-1)

            ### Compute log q(u|X) where u is the shared latent
            # Get all the individual log q(u|x_i) for all modalities
            if hasattr(inputs, "masks"):
                qu_x = []
                for m in posteriors:
                    qu = posteriors[m]["u"].log_prob(u).sum(-1)
                    # for unavailable modalities, set the log prob to -infinity so that it accounts for 0
                    # in the log_sum_exp.
                    qu[torch.stack([inputs.masks[m] == False] * len(u))] = -torch.inf
                    qu_x.append(qu)
                lqu_x = torch.stack(qu_x)  # n_modalities,K,nbatch
            else:
                lqu_x = torch.stack(
                    [posteriors[m]["u"].log_prob(u).sum(-1) for m in posteriors]
                )  # n_modalities,K,nbatch

            # Compute the mixture of experts
            lqu_x = torch.logsumexp(lqu_x, dim=0) - torch.log(
                n_mods_sample
            )  # log_mean_exp

            ### Compute log q(w |x_m)
            lqw_x = posteriors[mod]["w"].log_prob(w).sum(-1)

            ### Compute log p(X|u,w) for all modalities
            lpx_z = 0
            for recon_mod in reconstructions[mod]:
                x_recon = reconstructions[mod][recon_mod]
                K, n_batch = x_recon.shape[0], x_recon.shape[1]
                lpx_z_mod = (
                    self.recon_log_probs[recon_mod](x_recon, inputs[recon_mod])
                    .view(K, n_batch, -1)
                    .mul(self.rescale_factors[recon_mod])
                    .sum(-1)
                )

                if hasattr(inputs, "masks"):
                    # cancel unavailable modalities
                    lpx_z_mod *= inputs.masks[recon_mod].float()
                
                print(mod, lpx_z_mod.shape)

                lpx_z += lpx_z_mod

            ### Compute the entire likelihood
            lw = lpx_z + self.beta * (lpz - lqu_x - lqw_x)

            if hasattr(inputs, "masks"):
                # cancel unavailable modalities
                lw *= inputs.masks[mod].float()

            lws[mod] = lw

        return lws, n_mods_sample

    def dreg_looser(self, posteriors, embeddings, reconstructions, inputs):
        """
        The DreG estimation for IWAE. losses components in lws needs to have been computed on
        **detached** posteriors.
        """
        lws, n_mods_sample = self.compute_k_lws(
            posteriors, embeddings, reconstructions, inputs
        )

        ### Compute the wk for each modality
        wk = {}
        with torch.no_grad():  # The wk are constants
            for m, lw in lws.items():
                wk[m] = (
                    lw - torch.logsumexp(lw, 0, keepdim=True)
                ).exp()  # K, batch_size

        ### Compute the loss
        lws = torch.stack(
            [lws[mod] * wk[mod] for mod in lws], dim=0
        )  # n_modalities, K, batch_size
        lws = lws.sum(1)  # Sum over the k_iwae samples

        ### Take the mean over the modalities (outside the log)
        lws = lws.sum(0) / n_mods_sample

        # The gradient with respect to \phi is multiplied one more time by wk
        # To achieve that, we register a hook on the latent variables u and w
        for mod in embeddings:
            embeddings[mod]["w"].register_hook(
                lambda grad, w=wk[mod]: w.unsqueeze(-1) * grad
            )
            embeddings[mod]["u"].register_hook(
                lambda grad, w=wk[mod]: w.unsqueeze(-1) * grad
            )

        ### Return the sum over the batch
        return ModelOutput(loss=-lws.sum(), loss_sum=-lws.sum(), metrics=dict())

    def iwae_looser(self, posteriors, embeddings, reconstructions, inputs):
        """
        The IWAE loss but with the sum outside of the loss for increased stability.
        (following Shi et al 2019)

        """
        # Get all individual likelihoods
        lws, n_mods_sample = self.compute_k_lws(
            posteriors, embeddings, reconstructions, inputs
        )
        lws = torch.stack(list(lws.values()), dim=0)  # (n_modalities, K, n_batch)

        # Take log_mean_exp on the k_iwae samples to obtain the k-sampled estimate
        lws = torch.logsumexp(lws, dim=1) - math.log(
            lws.size(1)
        )  # n_modalities, n_batch

        # Take the mean on modalities
        lws = lws.sum(0) / n_mods_sample

        # Return the sum over the batch
        return ModelOutput(loss=-lws.sum(), loss_sum=-lws.sum(), metrics=dict())