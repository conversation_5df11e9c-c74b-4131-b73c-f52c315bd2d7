import torch
import math
import torch.nn as nn

from itertools import chain, combinations

import torch.nn.functional as F
from torch.distributions import <PERSON><PERSON>, Normal
from pythae.models.base.base_utils import ModelOutput
import torch.distributions as dist

from models.base import BaseModel

class MoPoE(BaseModel):
    def __init__(self, model_config, encoders, decoders) -> None:
        super().__init__(model_config, encoders, decoders)
        print('current model is mopoe')

        self.model_config = model_config

        self.beta = model_config.beta
        self.multiple_latent_spaces = model_config.use_modality_specific_spaces

        list_subsets = self.model_config.subsets
        if type(list_subsets) == dict:
            list_subsets = list(list_subsets.values())
        if list_subsets is None:
            list_subsets = self.all_subsets()
        self.set_subsets(list_subsets)

    def poe(self, mu, logvar, eps=1e-8):
        var = torch.exp(logvar) + eps
        # precision of i-th Gaussian expert at point x
        T = 1.0 / var
        pd_mu = torch.sum(mu * T, dim=0) / torch.sum(T, dim=0)
        pd_var = 1.0 / torch.sum(T, dim=0)
        pd_logvar = torch.log(pd_var)
        return pd_mu, pd_logvar

    def poe_fusion(self, mus: torch.Tensor, logvars: torch.Tensor, weights=None):
        # Following the original implementation : add the prior when we consider the
        # subset that contains all the modalities
        if mus.shape[0] == len(self.encoders.keys()):
            num_samples = mus[0].shape[0]
            device = mus.device
            mus = torch.cat(
                (mus, torch.zeros(1, num_samples, self.latent_dim).to(device)), dim=0
            )
            logvars = torch.cat(
                (logvars, torch.zeros(1, num_samples, self.latent_dim).to(device)),
                dim=0,
            )
        mu_poe, logvar_poe = self.poe(mus, logvars)
        return [mu_poe, logvar_poe]


    def all_subsets(self):
        """
        Returns a list containing all possible subsets of the modalities.
        (But the empty one)
        """
        # xs = list(self.encoders.keys())#.remove('union')
        # if 'union' in xs:
        #     xs.remove('union')
        xs = self.used_modalities
        # note we return an iterator rather than a list
        subsets_list = chain.from_iterable(
            combinations(xs, n) for n in range(len(xs) + 1)
        )
        return subsets_list

    def set_subsets(self, subsets_list):
        """
        Builds a dictionary of the subsets.
        The keys are the subset_names created by concatenating the modalities' names.
        The values are the list of modalities names.
        """
        subsets = dict()
        for k, mod_names in enumerate(subsets_list):
            mods = []
            for l, mod_name in enumerate(sorted(mod_names)):
                if (mod_name not in self.used_modalities) and (mod_name != ""):
                    raise AttributeError(
                        f"The provided subsets list contains unknown modality name {mod_name}."
                        " that is not the encoders dictionary or inputs_dim dictionary."
                    )
                mods.append(mod_name)
            key = "_".join(sorted(mod_names))
            subsets[key] = mods
        self.subsets = subsets
        self.model_config.subsets = subsets
        return
    
    def reparameterize(self, mu, logvar):
        std = logvar.mul(0.5).exp_()
        z = dist.Normal(mu, std).rsample()
        return z

    def calc_joint_divergence(
        self, mus: torch.Tensor, logvars: torch.Tensor, weights: torch.Tensor
    ):
        """Computes the KL divergence between the mixture of experts and the prior, by
        developping into the sum of the tractable KLs divergences of each expert.

        Args:
            mus (Tensor): The means of the experts. (n_subset,n_samples, latent_dim)
            logvars (Tensor): The logvars of the experts.(n_subset,n_samples, latent_dim)
            weights (Tensor): The weights of the experts.(n_subset,n_samples)


        Returns:
            Tensor, Tensor: The group divergence summed over modalities, A tensor containing the KL terms for each experts.
        """
        weights = weights.clone()

        num_mods = mus.shape[0]
        num_samples = mus.shape[1]
        klds = torch.zeros(num_mods, num_samples)

        device = mus.device
        klds = klds.to(device)
        weights = weights.to(device)
        for k in range(0, num_mods):
            kld_ind = -0.5 * (
                1 - logvars[k, :, :].exp() - mus[k, :, :].pow(2) + logvars[k, :, :]
            ).sum(-1)

            klds[k, :] = kld_ind

        group_div = (
            (weights * klds).sum(dim=0).mean()
        )  # sum over experts, mean over samples

        divs = dict()
        divs["joint_divergence"] = group_div
        return divs

    def forward(self, x):
        # Compute latents parameters for all subsets
        latents = self.inference(x)
        results = dict()

        # Get the embeddings for shared latent space
        shared_embeddings = self.reparameterize(
            latents["joint"][0], latents["joint"][1]
        )
        len_batch = shared_embeddings.shape[0]

        # Compute the divergence to the prior
        div = self.calc_joint_divergence(
            latents["mus"], latents["logvars"], latents["weights"]
        )
        for k, key in enumerate(div.keys()):
            results[key] = div[key]

        embeddings = {}
        for mod, latent in latents['modalities'].items():
            embeddings[mod] = latent['embedding']
            #print('embedding', latent['embedding'].shape)
        embeddings['fusion'] = latents['joint'][0]

        reconstructions = {}
        reconstructions['fusion'] = {}

        # Compute the reconstruction losses for each modality
        loss = 0
        kld = results["joint_divergence"]
        #for m_key in self.used_modalities:
        for m_key in x.keys():
            if m_key in self.used_modalities:
                # reconstruct this modality from the shared embeddings representation
                if self.multiple_latent_spaces:
                    try:
                        # sample from the modality specific latent space
                        style_mu = latents["modalities"][m_key].style_embedding
                        style_log_var = latents["modalities"][m_key].style_log_covariance
                        style_embeddings = self.reparameterize(style_mu, style_log_var)
                        full_embedding = torch.cat(
                            [shared_embeddings, style_embeddings], dim=-1
                        )
                    except:
                        raise AttributeError(
                            " model_config.use_modality_specific_spaces is True "
                            f"but encoder output for modality {m_key} doesn't have a "
                            "style_embedding attribute. "
                            "When using multiple latent spaces, the encoders' output"
                            "should be of the form : ModelOuput(embedding = ...,"
                            "style_embedding = ...,log_covariance = ..., style_log_covariance = ...)"
                        )
                else:
                    full_embedding = shared_embeddings

                #recon = self.decoders[m_key](full_embedding).reconstruction
                recon = self.decoding(full_embedding, m_key)
                reconstructions['fusion'][m_key] = recon

                m_rec = (
                    (
                        -self.recon_log_probs[m_key](recon, x[m_key])
                        * self.rescale_factors[m_key]
                    )
                    .view(recon.size(0), -1)
                    .sum(-1)
                )

                # reconstruction loss
                if hasattr(x, "masks"):
                    results["recon_" + m_key] = (m_rec * x.masks[m_key].float()).mean()
                    # We still take the mean over the all batch even if some samples are missing
                    # in order to give the same weights to all samples between batches
                else:
                    results["recon_" + m_key] = m_rec.mean()

                loss += results["recon_" + m_key]

                # If using modality specific latent spaces, add modality specific klds
                if self.multiple_latent_spaces:
                    style_mu = latents["modalities"][m_key].style_embedding
                    style_log_var = latents["modalities"][m_key].style_log_covariance
                    style_kld = -0.5 * (
                        1 - style_log_var.exp() - style_mu.pow(2) + style_log_var
                    ).view(style_mu.size(0), -1).sum(-1)

                    if hasattr(x, "masks"):
                        style_kld *= x.masks[m_key].float()

                    kld += style_kld.mean() * self.model_config.beta_style

        loss = loss + self.beta * kld

        loss_output =  ModelOutput(loss=loss, loss_sum=loss * len_batch, metrics=results)
        loss_output['zss'] = embeddings
        #print('embeddings', embeddings.keys())


        for con_mod in x.keys():
            if con_mod in self.used_modalities:
                reconstructions[con_mod] = {}
                for rec_mod in x.keys():
                    if rec_mod in self.used_modalities:
                        full_embedding = self.reparameterize(
                            latents["modalities"][con_mod]["embedding"], latents["modalities"][con_mod]["log_covariance"]
                        )
                        recon = self.decoding(full_embedding, rec_mod)
                        #print('recon', recon.shape)
                        reconstructions[con_mod][rec_mod] = recon
        #print('reconst', reconstructions.keys())
        loss_output["recon"] = reconstructions
        loss_output["sample_k"] = False
        return loss_output
    
    def forward_recon(self, voxel, modality):
        output = self.encoding(voxel, modality)
        mu, log_var = output['embedding'], output['log_covariance']

        z_x = self.reparameterize(mu, log_var)

        def get_recon(z_x, recon_mod):
            z = z_x.reshape(-1, z_x.shape[-1])
            recon = self.decoding(z, recon_mod)
            recon = recon.reshape((*z_x.shape[:-1], *recon.shape[1:]))
            return recon
        
        voxel2img = get_recon(z_x, 'image')
        voxel2txt = get_recon(z_x, 'text')
        return voxel2img, voxel2txt

    

    def modality_encode(
        self, inputs
    ):
        """

        Computes for each modality, the parameters mu and logvar of the
        unimodal posterior.

        Args:
            inputs (MultimodalBaseDataset): The data to encode.

        Returns:
            dict: Containing for each modality the encoder output.
        """
        encoders_outputs = dict()
        #for m, m_key in enumerate(self.used_modalities):
        for m_key in inputs.keys():
            if m_key in self.used_modalities:
                input_modality = inputs[m_key]
                output = self.encoding(input_modality, m_key)
                encoders_outputs[m_key] = output

        return encoders_outputs

    def inference(self, inputs):
        """

        Args:
            inputs (MultimodalBaseDataset): The data.

        Returns:
            dict: all the subset and joint posteriors parameters.
        """

        latents = dict()
        enc_mods = self.modality_encode(inputs)
        latents["modalities"] = enc_mods
        device = inputs[list(inputs.keys())[0]].device

        mus = torch.Tensor().to(device)
        logvars = torch.Tensor().to(device)
        distr_subsets = dict()
        availabilities = []

        for k, s_key in enumerate(self.subsets.keys()):
            if s_key == "" or 'fmri' not in inputs.keys() and 'fmri' in s_key or 'eeg' not in inputs.keys() and 'eeg' in s_key:
                continue
            else:
                mods = self.subsets[s_key]
                mus_subset = torch.Tensor().to(device)
                logvars_subset = torch.Tensor().to(device)

                if hasattr(inputs, "masks"):
                    filter = self.subset_mask(inputs, mods)
                    availabilities.append(filter)

                for m, mod in enumerate(mods):
                    mus_mod = enc_mods[mod]['embedding']
                    log_vars_mod = enc_mods[mod]['log_covariance']

                    mus_subset = torch.cat((mus_subset, mus_mod.unsqueeze(0)), dim=0)

                    logvars_subset = torch.cat(
                        (logvars_subset, log_vars_mod.unsqueeze(0)), dim=0
                    )
                # Case with only one sample : adapt the shape
                if len(mus_subset.shape) == 2:
                    mus_subset = mus_subset.unsqueeze(1)
                    logvars_subset = logvars_subset.unsqueeze(1)

                s_mu, s_logvar = self.poe_fusion(mus_subset, logvars_subset)

                distr_subsets[s_key] = [s_mu, s_logvar]

                # Add the subset posterior to be part of the mixture of experts
                mus = torch.cat((mus, s_mu.unsqueeze(0)), dim=0)
                logvars = torch.cat((logvars, s_logvar.unsqueeze(0)), dim=0)

        if hasattr(inputs, "masks"):
            # if we have an incomplete dataset, we need to randomly choose
            # from the mixture of available experts
            availabilities = torch.stack(availabilities, dim=0).float()
            if len(availabilities.shape) == 1:
                availabilities = availabilities.unsqueeze(1)
            availabilities /= torch.sum(availabilities, dim=0)  # (n_subset,n_samples)

            joint_mu, joint_logvar = self.random_mixture_component_selection(
                mus, logvars, availabilities
            )
            weights = availabilities
        else:
            weights = (1 / float(mus.shape[0])) * torch.ones(mus.shape[0]).to(device)
            joint_mu, joint_logvar = self.deterministic_mixture_component_selection(
                mus, logvars, weights
            )
            weights = (1 / float(mus.shape[0])) * torch.ones(
                mus.shape[0], mus.shape[1]
            ).to(device)

        latents["mus"] = mus
        latents["logvars"] = logvars
        latents["weights"] = weights
        latents["joint"] = [joint_mu, joint_logvar]
        latents["subsets"] = distr_subsets
        return latents
    
    def deterministic_mixture_component_selection(self, mus, logvars, w_modalities):
        """
        Associate a subset mu and log_covariance per sample in a balanced way, so that the proportion
        of samples per subset correspond to w_modalities.
        """

        num_components = mus.shape[0]  # number of components
        num_samples = mus.shape[1]

        idx_start = []
        idx_end = []
        for k in range(0, num_components):
            if k == 0:
                i_start = 0
            else:
                i_start = int(idx_end[k - 1])
            if k == w_modalities.shape[0] - 1:
                i_end = num_samples
            else:
                i_end = i_start + int(torch.floor(num_samples * w_modalities[k]))
            idx_start.append(i_start)
            idx_end.append(i_end)
        idx_end[-1] = num_samples
        mu_sel = torch.cat(
            [mus[k, idx_start[k] : idx_end[k], :] for k in range(w_modalities.shape[0])]
        )
        logvar_sel = torch.cat(
            [
                logvars[k, idx_start[k] : idx_end[k], :]
                for k in range(w_modalities.shape[0])
            ]
        )
        return [mu_sel, logvar_sel]