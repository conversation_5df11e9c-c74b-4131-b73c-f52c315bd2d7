from .base_arch import ResML<PERSON>, ConvModule
import torch.nn as nn

def create_brain_encoder(in_dim_brain, h, drop_rate=0.2, num_mlp=8):
    brain_encoder = nn.Sequential(
                nn.Linear(in_dim_brain, h),
                nn.LayerNorm(h),
                nn.GELU(),
                nn.Dropout(drop_rate),
                ResMLP(h, num_mlp), 
                nn.Linear(h, h),
            )
    return brain_encoder
        

def create_clip_encoder(h, reduce_channel, channel, clip_size=768, drop_rate=0.2):
    clip_encoder = nn.Sequential(
        #ConvModule(channel, reduce_channel, clip_size, h),
        nn.Linear(clip_size, h),
        nn.LayerNorm(h),
        nn.GELU(),
        nn.Dropout(drop_rate),
        nn.Linear(h, h),
    )
    return clip_encoder


def create_union_encoder(h, drop_rate=0.2):
    union_encoder = nn.Sequential(
        nn.Linear(h, h//2),
        nn.LayerNorm(h//2),
        nn.GELU(),
        nn.Dropout(drop_rate),
        nn.Linear(h//2, h),
    )
    return union_encoder


def create_sub_encoder(len_subj, sub_dim):
    sub_encoder = nn.Sequential(
                nn.Linear(len_subj, sub_dim),
                nn.LayerNorm(sub_dim),
                nn.GELU(),
                nn.Linear(sub_dim, sub_dim),
                nn.LayerNorm(sub_dim),
                nn.GELU(),
                nn.Linear(sub_dim, sub_dim),
            )
    return sub_encoder